from django.views.generic import TemplateView
from rest_framework import generics
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import Unidade, Interessado, Localizacao, Assunto, Local, Especialidade
from .serializers import UnidadeSerializer, InteressadoSerializer, LocalizacaoSerializer, AssuntoSerializer, LocalSerializer, EspecialidadeSerializer

class SeiFormView(TemplateView):
    template_name = 'sei/form.html'

class UnidadeListByCodigo(generics.ListAPIView):
    serializer_class = UnidadeSerializer
    def get_queryset(self):
        return Unidade.objects.values_list('codigo', flat=True).distinct()

class UnidadeListByNome(generics.ListAPIView):
    serializer_class = UnidadeSerializer
    def get_queryset(self):
        return Unidade.objects.values_list('unidade', flat=True).distinct()

class UnidadeListByCodigoOrNome(APIView):
    def get(self, request, format=None):
        unidades = Unidade.objects.all()
        # Format each object as a string
        formatted_unidades = [str(unidade) for unidade in unidades]
        return Response(sorted(list(set(formatted_unidades))))

class InteressadoListByCodigo(generics.ListAPIView):
    serializer_class = InteressadoSerializer
    def get_queryset(self):
        return Interessado.objects.values_list('codigo', flat=True).distinct()

class InteressadoListByConcessao(generics.ListAPIView):
    serializer_class = InteressadoSerializer
    def get_queryset(self):
        return Interessado.objects.values_list('concessao', flat=True).distinct()

class InteressadoListByCodigoOrConcessao(APIView):
    def get(self, request, format=None):
        codigos = list(Interessado.objects.values_list('codigo', flat=True).distinct())
        concessoes = list(Interessado.objects.values_list('concessao', flat=True).distinct())
        return Response(sorted(list(set(codigos + concessoes))))

class LocalizacaoListByCodigo(generics.ListAPIView):
    serializer_class = LocalizacaoSerializer
    def get_queryset(self):
        return Localizacao.objects.values_list('codigo', flat=True).distinct()

class LocalizacaoListByNome(generics.ListAPIView):
    serializer_class = LocalizacaoSerializer
    def get_queryset(self):
        return Localizacao.objects.values_list('localizacao', flat=True).distinct()

class LocalizacaoListByCodigoOrNome(APIView):
    def get(self, request, format=None):
        codigos = list(Localizacao.objects.values_list('codigo', flat=True).distinct())
        nomes = list(Localizacao.objects.values_list('localizacao', flat=True).distinct())
        return Response(sorted(list(set(codigos + nomes))))

class AssuntoListByCodigo(generics.ListAPIView):
    serializer_class = AssuntoSerializer
    def get_queryset(self):
        return Assunto.objects.values_list('codigo', flat=True).distinct()

class AssuntoListByDoc(generics.ListAPIView):
    serializer_class = AssuntoSerializer
    def get_queryset(self):
        return Assunto.objects.values_list('doc', flat=True).distinct()

class AssuntoListByCodigoOrDoc(APIView):
    def get(self, request, format=None):
        codigos = list(Assunto.objects.values_list('codigo', flat=True).distinct())
        docs = list(Assunto.objects.values_list('doc', flat=True).distinct())
        return Response(sorted(list(set(codigos + docs))))

class LocalListByCodigo(generics.ListAPIView):
    serializer_class = LocalSerializer
    def get_queryset(self):
        return Local.objects.values_list('codigo', flat=True).distinct()

class LocalListByLocalizacao(generics.ListAPIView):
    serializer_class = LocalSerializer
    def get_queryset(self):
        return Local.objects.values_list('localizacao', flat=True).distinct()

class LocalListByCodigoOrLocalizacao(APIView):
    def get(self, request, format=None):
        codigos = list(Local.objects.values_list('codigo', flat=True).distinct())
        localizacoes = list(Local.objects.values_list('localizacao', flat=True).distinct())
        return Response(sorted(list(set(codigos + localizacoes))))

class EspecialidadeListByCodigo(generics.ListAPIView):
    serializer_class = EspecialidadeSerializer
    def get_queryset(self):
        return Especialidade.objects.values_list('codigo', flat=True).distinct()

class EspecialidadeListByNome(generics.ListAPIView):
    serializer_class = EspecialidadeSerializer
    def get_queryset(self):
        return Especialidade.objects.values_list('especialidade', flat=True).distinct()

class EspecialidadeListByCodigoOrNome(APIView):
    def get(self, request, format=None):
        codigos = list(Especialidade.objects.values_list('codigo', flat=True).distinct())
        nomes = list(Especialidade.objects.values_list('especialidade', flat=True).distinct())
        return Response(sorted(list(set(codigos + nomes))))
