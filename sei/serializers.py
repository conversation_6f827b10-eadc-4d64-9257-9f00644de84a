from rest_framework import serializers
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Protocolo

class RequisitanteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Requisitante
        fields = ['nome', 'email', 'ip_address']

class ServicoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Servico
        fields = ['codigo', 'tipo_servico', 'comentarios']

    def validate_codigo(self, value):
        """
        Validate that codigo contains only numeric characters.
        """
        if value and not value.isdigit():
            raise serializers.ValidationError(
                "O código deve conter apenas números."
            )
        return value

class SeiFormSubmissionSerializer(serializers.Serializer):
    """
    Serializer for the complete SEI form submission
    """
    requisitante = RequisitanteSerializer()
    unidade_id = serializers.UUIDField(required=False, allow_null=True)
    interessado_id = serializers.UUIDField(required=False, allow_null=True)
    localizacao_id = serializers.UUIDField(required=False, allow_null=True)
    assunto_id = serializers.UUIDField(required=False, allow_null=True)
    servico = ServicoSerializer(required=False)
    local_id = serializers.UUIDField(required=False, allow_null=True)
    disciplina_id = serializers.UUIDField(required=False, allow_null=True)
    doc_revisao = serializers.CharField(max_length=255, required=False, allow_blank=True)
    doc_sei_num = serializers.CharField(max_length=255, required=False, allow_blank=True)

    def validate_doc_revisao(self, value):
        """
        Validate that doc_revisao follows the R## format if provided.
        """
        if not value:  # Allow empty values
            return value

        if not value.startswith('R') or len(value) != 3:
            raise serializers.ValidationError(
                "Revisão deve estar no formato R## (ex: R01, R15)."
            )
        try:
            num = int(value[1:])
            if num < 1 or num > 99:
                raise ValueError()
        except ValueError:
            raise serializers.ValidationError(
                "Revisão deve ser um número entre 01 e 99."
            )
        return value

    def validate_doc_sei_num(self, value):
        """
        Validate that doc_sei_num contains only numeric characters if provided.
        """
        if not value:  # Allow empty values
            return value

        if not value.isdigit():
            raise serializers.ValidationError(
                "Número SEI deve conter apenas números."
            )
        return value

class ProtocoloSerializer(serializers.ModelSerializer):
    class Meta:
        model = Protocolo
        fields = ['id', 'doc_cod', 'doc_revisao', 'doc_sei_num', 'created_at']