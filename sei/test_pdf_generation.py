"""
Test script to verify the PDF generation system for SEI protocols.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
import json
import uuid


class SeiPdfGenerationTest(TestCase):
    """Test cases for PDF generation functionality"""
    
    def setUp(self):
        self.client = Client()
        self.submit_url = reverse('sei:form_submit')
        
        # Valid form data for creating a test protocol
        self.valid_data = {
            'requisitante': {
                'nome': '<PERSON>',
                'email': '<EMAIL>'
            },
            'unidade_id': None,
            'interessado_id': None,
            'localizacao_id': None,
            'assunto_id': None,
            'servico_codigo': '123',
            'servico_tipo': 'Análise Técnica',
            'local_id': None,
            'disciplina_id': None,
            'doc_revisao': '',
            'doc_sei_num': ''
        }
    
    def create_test_protocol(self):
        """Helper method to create a test protocol"""
        response = self.client.post(
            self.submit_url,
            data=json.dumps(self.valid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        result = response.json()
        return result['protocolo_id']
    
    def test_pdf_generation_view_exists(self):
        """Test that the PDF generation URL pattern exists"""
        protocolo_id = str(uuid.uuid4())
        pdf_url = reverse('sei:protocolo_pdf', kwargs={'protocolo_id': protocolo_id})
        
        # URL should be properly formatted
        expected_url = f'/sei/protocolo/{protocolo_id}/pdf/'
        self.assertEqual(pdf_url, expected_url)
    
    def test_pdf_html_preview_view_exists(self):
        """Test that the PDF HTML preview URL pattern exists"""
        protocolo_id = str(uuid.uuid4())
        html_url = reverse('sei:protocolo_pdf_html', kwargs={'protocolo_id': protocolo_id})
        
        # URL should be properly formatted
        expected_url = f'/sei/protocolo/{protocolo_id}/pdf/html/'
        self.assertEqual(html_url, expected_url)
    
    def test_pdf_generation_with_valid_protocol(self):
        """Test PDF generation with a valid protocol"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test PDF generation
        pdf_url = reverse('sei:protocolo_pdf', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(pdf_url)
        
        # Should return PDF or error message (depending on xhtml2pdf availability)
        self.assertIn(response.status_code, [200, 500])
        
        if response.status_code == 200:
            # Should be a PDF file
            self.assertEqual(response['Content-Type'], 'application/pdf')
            self.assertIn('attachment', response['Content-Disposition'])
            self.assertIn('.pdf', response['Content-Disposition'])
        else:
            # Should be an error message about missing xhtml2pdf
            self.assertIn('xhtml2pdf', response.content.decode())
    
    def test_pdf_html_preview_with_valid_protocol(self):
        """Test PDF HTML preview with a valid protocol"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test HTML preview
        html_url = reverse('sei:protocolo_pdf_html', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(html_url)
        
        # Should return HTML page
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/html; charset=utf-8')
        
        # Should contain protocol information
        self.assertContains(response, 'JOÃO SILVA SANTOS')  # Formatted name
        self.assertContains(response, '<EMAIL>')  # Email
        self.assertContains(response, 'Sistema SEI')  # Page title
        self.assertContains(response, 'Protocolo de Solicitação')  # Subtitle
    
    def test_pdf_generation_with_invalid_protocol(self):
        """Test PDF generation with non-existent protocol"""
        fake_id = str(uuid.uuid4())
        pdf_url = reverse('sei:protocolo_pdf', kwargs={'protocolo_id': fake_id})
        
        response = self.client.get(pdf_url)
        
        # Should redirect to form page
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('sei:sei_form'))
    
    def test_pdf_html_preview_with_invalid_protocol(self):
        """Test PDF HTML preview with non-existent protocol"""
        fake_id = str(uuid.uuid4())
        html_url = reverse('sei:protocolo_pdf_html', kwargs={'protocolo_id': fake_id})
        
        response = self.client.get(html_url)
        
        # Should redirect to form page
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('sei:sei_form'))
    
    def test_pdf_filename_generation(self):
        """Test that PDF filename is generated correctly"""
        from sei.utils import generate_pdf_filename, sanitize_filename
        from sei.models import Protocolo
        
        # Test sanitize_filename function
        test_cases = [
            ('ABC-123-DEF', 'ABC-123-DEF.pdf'),
            ('Test/File\\Name', 'Test_File_Name.pdf'),
            ('File<>Name', 'File__Name.pdf'),
            ('', 'protocolo.pdf'),
            ('   ', 'protocolo.pdf'),
        ]
        
        for input_name, expected in test_cases:
            if input_name in ['', '   ']:
                # Test with mock protocolo object
                class MockProtocolo:
                    doc_cod = input_name
                    id = uuid.uuid4()
                
                result = generate_pdf_filename(MockProtocolo())
                self.assertEqual(result, expected)
            else:
                result = sanitize_filename(input_name) + '.pdf'
                self.assertEqual(result, expected)
    
    def test_success_page_has_pdf_download_button(self):
        """Test that the success page includes the PDF download button"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Visit success page
        success_url = reverse('sei:success', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(success_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Should contain PDF download button
        self.assertContains(response, 'Download PDF')
        
        # Should contain correct PDF URL
        expected_pdf_url = reverse('sei:protocolo_pdf', kwargs={'protocolo_id': protocolo_id})
        self.assertContains(response, expected_pdf_url)
    
    def test_pdf_template_structure(self):
        """Test that the PDF template has the correct structure"""
        # Create a test protocol
        protocolo_id = self.create_test_protocol()
        
        # Test HTML preview to verify template structure
        html_url = reverse('sei:protocolo_pdf_html', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(html_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check for required sections
        required_sections = [
            'Dados do Requisitante',
            'Unidade',
            'Interessado',
            'Localização',
            'Assunto',
            'Serviço',
            'Local',
            'Disciplina',
            'Informações Adicionais'
        ]
        
        for section in required_sections:
            self.assertContains(response, section)
        
        # Check for document code display
        self.assertContains(response, 'Código do Documento')
        
        # Check for footer information
        self.assertContains(response, '© 2025 Sistema SEI')


class PdfUtilsTest(TestCase):
    """Test cases for PDF utility functions"""
    
    def test_validate_protocolo_access(self):
        """Test the protocolo access validation function"""
        from sei.utils import validate_protocolo_access
        
        # Currently should allow access to all protocols
        test_id = str(uuid.uuid4())
        self.assertTrue(validate_protocolo_access(test_id))
        self.assertTrue(validate_protocolo_access(test_id, None))
    
    def test_get_pdf_context(self):
        """Test the PDF context generation function"""
        from sei.utils import get_pdf_context
        
        # Create a mock protocolo object
        class MockProtocolo:
            doc_cod = 'TEST-123-ABC'
            created_at = '2025-01-01 12:00:00'
        
        protocolo = MockProtocolo()
        context = get_pdf_context(protocolo)
        
        # Should contain required context keys
        self.assertIn('protocolo', context)
        self.assertIn('title', context)
        self.assertIn('generated_at', context)
        
        # Should have correct values
        self.assertEqual(context['protocolo'], protocolo)
        self.assertEqual(context['title'], 'Protocolo TEST-123-ABC')


if __name__ == '__main__':
    print("Testing SEI PDF generation system...")
    print("Run with: python manage.py test sei.test_pdf_generation")
