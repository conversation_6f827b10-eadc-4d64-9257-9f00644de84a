<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulário SEI - Sistema de Informações</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- jQuery (required for Select2) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#FF161F',
                        'primary-dark': '#E6141C',
                        'primary-light': '#FF3A42',
                        'secondary': '#034EA2',
                        'secondary-dark': '#02407A',
                        'accent': '#0B9247',
                        'accent-dark': '#0A7A3A',
                        'highlight': '#FBB900',
                        'highlight-dark': '#E6A600'
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        /* Select2 Custom Styling */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single {
            height: 42px !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem !important;
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 26px !important;
            padding-left: 0 !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 40px !important;
            right: 8px !important;
        }

        .select2-container--default.select2-container--focus .select2-selection--single {
            border-color: #FF161F !important;
            box-shadow: 0 0 0 1px #FF161F !important;
        }

        /* Ensure Select2 dropdown is full width */
        .select2-dropdown {
            width: 100% !important;
        }

        /* Progress Bar Animation */
        .progress-bar {
            transition: width 0.3s ease-in-out;
        }

        /* Step Indicator Animation */
        .step-indicator {
            transition: all 0.3s ease-in-out;
        }

        /* Form Animation */
        .form-step {
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        .form-step.hidden {
            opacity: 0;
            transform: translateX(20px);
        }

        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #FF161F;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #E6141C;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div x-data="seiForm()" class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Sistema de Informações SEI</h1>
            <p class="text-gray-600">Preencha as informações em 11 etapas para completar o cadastro</p>
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">Progresso</span>
                <span class="text-sm font-medium text-primary" x-text="`${Math.round(((currentStep + 1) / 11) * 100)}%`"></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-primary h-2 rounded-full progress-bar"
                     :style="`width: ${((currentStep + 1) / 11) * 100}%`"></div>
            </div>
        </div>

        <!-- Step Indicators -->
        <div class="flex justify-between mb-8 overflow-x-auto custom-scrollbar">
            <template x-for="(step, index) in steps" :key="index">
                <div class="flex flex-col items-center min-w-0 flex-1 px-2">
                    <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium mb-2 cursor-pointer"
                         :class="{
                             'bg-primary text-white': index === currentStep,
                             'bg-green-500 text-white': index < currentStep && isStepValid(index),
                             'bg-gray-300 text-gray-600': index > currentStep,
                             'bg-red-500 text-white': index < currentStep && !isStepValid(index)
                         }"
                         @click="navigateToStep(index)">
                        <span x-show="index < currentStep && isStepValid(index)">✓</span>
                        <span x-show="!(index < currentStep && isStepValid(index))" x-text="index + 1"></span>
                    </div>
                    <span class="text-xs text-center text-gray-600 leading-tight" x-text="step.title"></span>
                </div>
            </template>
        </div>

        <!-- Error Display -->
        <div x-show="errors.length > 0" class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Erro na validação</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <template x-for="error in errors" :key="error">
                                <li x-text="error"></li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Container -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <!-- Step 0: Requisitante (Usuario) -->
            <div x-show="currentStep === 0" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">1</span>
                    Informações do Requisitante
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="nome" class="block text-sm font-medium text-gray-700 mb-2">
                            Nome Completo <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="nome"
                               x-model="formData.requisitante.nome"
                               @input="saveToSession()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               :class="{'border-red-500': errors.includes('Nome é obrigatório')}"
                               placeholder="Digite seu nome completo">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            E-mail <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="email"
                               x-model="formData.requisitante.email"
                               @input="saveToSession()"
                               @paste.prevent
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               :class="{'border-red-500': errors.includes('E-mail é obrigatório') || errors.includes('E-mails não coincidem')}"
                               placeholder="<EMAIL>">
                        <p class="text-xs text-red-500 mt-1">⚠️ Colar não permitido - digite manualmente</p>
                    </div>

                    <div>
                        <label for="email_confirm" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirmar E-mail <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="email_confirm"
                               x-model="formData.requisitante.email_confirm"
                               @input="saveToSession()"
                               @paste.prevent
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               :class="{'border-red-500': errors.includes('Confirmação de e-mail é obrigatória') || errors.includes('E-mails não coincidem')}"
                               placeholder="Confirme seu e-mail">
                        <p class="text-xs text-red-500 mt-1">⚠️ Colar não permitido - digite manualmente</p>
                    </div>
                </div>
            </div>

            <!-- Step 1: Unidade -->
            <div x-show="currentStep === 1" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-secondary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                    Unidade
                </h2>

                <div class="w-full">
                    <label for="unidade_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione uma Unidade <span class="text-red-500">*</span>
                    </label>
                    <select id="unidade_select"
                            class="w-full select2-unidade"
                            :class="{'border-red-500': errors.includes('Seleção de unidade é obrigatória')}">
                        <option value="">Selecione uma unidade ou digite para buscar</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar</p>
                </div>
            </div>

            <!-- Step 2: Interessado -->
            <div x-show="currentStep === 2" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-accent text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                    Interessado
                </h2>

                <div class="w-full">
                    <label for="interessado_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione um Interessado <span class="text-red-500">*</span>
                    </label>
                    <select id="interessado_select"
                            class="w-full select2-interessado"
                            :class="{'border-red-500': errors.includes('Seleção de interessado é obrigatória')}">
                        <option value="">Selecione um interessado ou digite para buscar</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/concessão.</p>
                </div>
            </div>

            <!-- Step 3: Localização -->
            <div x-show="currentStep === 3" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-highlight text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">4</span>
                    Localização
                </h2>

                <div class="w-full">
                    <label for="localizacao_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione uma Localização <span class="text-red-500">*</span>
                    </label>
                    <select id="localizacao_select"
                            class="w-full select2-localizacao"
                            :class="{'border-red-500': errors.includes('Seleção de localização é obrigatória')}">
                        <option value="">Selecione uma localização ou digite para buscar</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/localização.</p>
                </div>
            </div>

            <!-- Step 4: Assunto -->
            <div x-show="currentStep === 4" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">5</span>
                    Assunto
                </h2>

                <div class="w-full">
                    <label for="assunto_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione um Assunto <span class="text-red-500">*</span>
                    </label>
                    <select id="assunto_select"
                            class="w-full select2-assunto"
                            :class="{'border-red-500': errors.includes('Seleção de assunto é obrigatória')}">
                        <option value="">Selecione um assunto ou digite para buscar</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/documento.</p>
                </div>
            </div>

            <!-- Step 5: Serviço -->
            <div x-show="currentStep === 5" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-accent text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">6</span>
                    Serviço
                </h2>

                <div class="bg-accent-light bg-opacity-10 border border-accent rounded-lg p-4 mb-6">
                    <p class="text-accent-dark text-sm">
                        <strong>Campos Opcionais:</strong> Ambos os campos são opcionais.
                        Você pode prosseguir sem preencher nenhum campo.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl">
                    <div>
                        <label for="servico_codigo" class="block text-sm font-medium text-gray-700 mb-2">
                            Código
                        </label>
                        <input type="text"
                               id="servico_codigo"
                               x-model="formData.servico.codigo"
                               @input="validateServicoCodigoInput($event); saveToSession()"
                               @keypress="validateNumericInput($event)"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
                               :class="{'border-red-500': errors.includes('Código do serviço deve conter apenas números')}"
                               placeholder="Digite o código (apenas números, opcional)">
                        <p class="text-xs text-gray-500 mt-1">⚠️ Apenas números são permitidos</p>
                    </div>

                    <div>
                        <label for="servico_tipo_servico" class="block text-sm font-medium text-gray-700 mb-2">
                            Tipo de Serviço
                        </label>
                        <input type="text"
                               id="servico_tipo_servico"
                               x-model="formData.servico.tipo_servico"
                               @input="saveToSession()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
                               placeholder="Digite o tipo de serviço (opcional)">
                    </div>
                </div>
            </div>

            <!-- Step 6: Local -->
            <div x-show="currentStep === 6" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-secondary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">7</span>
                    Local
                </h2>

                <div class="w-full">
                    <label for="local_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione um Local <span class="text-red-500">*</span>
                    </label>
                    <select id="local_select"
                            class="w-full select2-local"
                            :class="{'border-red-500': errors.includes('Seleção de local é obrigatória')}">
                        <option value="">Selecione um local ou digite para buscar</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/local.</p>
                </div>
            </div>

            <!-- Step 7: Disciplina -->
            <div x-show="currentStep === 7" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-highlight text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">8</span>
                    Disciplina
                </h2>

                <div class="w-full">
                    <label for="disciplina_select" class="block text-sm font-medium text-gray-700 mb-2">
                        Selecione uma Disciplina <span class="text-red-500">*</span>
                    </label>
                    <select id="disciplina_select"
                            class="w-full select2-disciplina"
                            :class="{'border-red-500': errors.includes('Seleção de disciplina é obrigatória')}">
                        <option value="">Selecione uma disciplina ou digite para buscar</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Clique para ver as primeiras opções ou digite para buscar por código/disciplina.</p>
                </div>
            </div>

            <!-- Step 8: Revisão -->
            <div x-show="currentStep === 8" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">9</span>
                    Revisão do Documento
                </h2>

                <div class="w-full max-w-md">
                    <label for="revisao_input" class="block text-sm font-medium text-gray-700 mb-2">
                        Revisão <span class="text-gray-500">(opcional)</span>
                    </label>
                    <input type="number"
                           id="revisao_input"
                           x-model="formData.revisao.value"
                           @input="formatRevisao(); saveToSession()"
                           @keypress="validateRevisaoInput($event)"
                           min="1"
                           max="99"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           :class="{'border-red-500': errors.includes('Revisão é obrigatória') || errors.includes('Revisão deve ser um número entre 1 e 99')}"
                           placeholder="Digite um número entre 1 e 99 (opcional)">
                    <p class="text-xs text-gray-500 mt-1">
                        Formato de exibição: <span x-text="formData.revisao.formatted || 'R01'" class="font-mono bg-gray-100 px-1 rounded"></span>
                    </p>
                    <p class="text-xs text-gray-500 mt-1">💡 Campo opcional - deixe em branco se não aplicável</p>
                </div>
            </div>

            <!-- Step 9: Número SEI -->
            <div x-show="currentStep === 9" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-secondary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">10</span>
                    Número SEI
                </h2>

                <div class="w-full max-w-md">
                    <label for="sei_number_input" class="block text-sm font-medium text-gray-700 mb-2">
                        Número SEI <span class="text-gray-500">(opcional)</span>
                    </label>
                    <input type="text"
                           id="sei_number_input"
                           x-model="formData.seiNumber.numero"
                           @input="validateSeiNumberInput($event); saveToSession()"
                           @keypress="validateNumericInput($event)"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"
                           :class="{'border-red-500': errors.includes('Número SEI é obrigatório') || errors.includes('Número SEI deve conter apenas números')}"
                           placeholder="Digite o número SEI (opcional, apenas números)">
                    <p class="text-xs text-gray-500 mt-1">💡 Campo opcional - deixe em branco se não aplicável</p>
                </div>
            </div>

            <!-- Step 10: Review & Submit -->
            <div x-show="currentStep === 10" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">11</span>
                    Revisão Final e Envio
                </h2>

                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <p class="text-green-800 text-sm">
                        <strong>Revisão Final:</strong> Verifique todas as informações antes de enviar o formulário.
                        Você pode voltar para editar qualquer etapa se necessário.
                    </p>
                </div>

                <!-- Summary of all form data -->
                <div class="space-y-6">
                    <!-- Requisitante Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">1. Requisitante</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Nome:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.requisitante.nome || 'Não informado'"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">E-mail:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.requisitante.email || 'Não informado'"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Unidade Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">2. Unidade</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.unidade.text || 'Nenhuma unidade selecionada'"></span>
                        </div>
                    </div>

                    <!-- Interessado Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">3. Interessado</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.interessado.text || 'Nenhum interessado selecionado'"></span>
                        </div>
                    </div>

                    <!-- Localização Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">4. Localização</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.localizacao.text || 'Nenhuma localização selecionada'"></span>
                        </div>
                    </div>

                    <!-- Assunto Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">5. Assunto</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.assunto.text || 'Nenhum assunto selecionado'"></span>
                        </div>
                    </div>

                    <!-- Serviço Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">6. Serviço</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Código:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.servico.codigo || 'Não informado'"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Tipo de Serviço:</span>
                                <span class="ml-2 text-gray-900" x-text="formData.servico.tipo_servico || 'Não informado'"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Local Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">7. Local</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.local.text || 'Nenhum local selecionado'"></span>
                        </div>
                    </div>

                    <!-- Disciplina Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">8. Disciplina</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Selecionado:</span>
                            <span class="ml-2 text-gray-900" x-text="formData.disciplina.text || 'Nenhuma disciplina selecionada'"></span>
                        </div>
                    </div>

                    <!-- Revisão Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">9. Revisão</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Revisão:</span>
                            <span class="ml-2 font-mono"
                                  :class="formData.revisao.formatted ? 'text-gray-900' : 'text-gray-500 italic'"
                                  x-text="formData.revisao.formatted || 'Não aplicável (campo opcional)'"></span>
                        </div>
                    </div>

                    <!-- Número SEI Summary -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">10. Número SEI</h3>
                        <div class="text-sm">
                            <span class="font-medium text-gray-700">Número:</span>
                            <span class="ml-2 font-mono"
                                  :class="formData.seiNumber.numero ? 'text-gray-900' : 'text-gray-500 italic'"
                                  x-text="formData.seiNumber.numero || 'Não aplicável (campo opcional)'"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="flex justify-between items-center">
            <button type="button"
                    @click="previousStep()"
                    x-show="currentStep > 0"
                    class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                ← Anterior
            </button>

            <div x-show="currentStep === 0" class="w-24"></div>

            <button type="button"
                    @click="nextStep()"
                    x-show="currentStep < 10"
                    class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors">
                Próximo →
            </button>

            <button type="button"
                    @click="submitForm()"
                    x-show="currentStep === 10"
                    class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-600 focus:ring-offset-2 transition-colors">
                Enviar Formulário
            </button>
        </div>

        <!-- Success Modal -->
        <div x-show="showSuccessModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Cadastro Realizado com Sucesso!</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                            Suas informações foram enviadas com sucesso. Você receberá uma confirmação por e-mail em breve.
                        </p>
                    </div>
                    <div class="items-center px-4 py-3">
                        <button @click="closeSuccessModal()"
                                class="px-4 py-2 bg-primary text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary">
                            Fechar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div x-show="isLoading"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-40"
             style="display: none;">
            <div class="relative top-1/2 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white transform -translate-y-1/2">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-light bg-opacity-20">
                        <svg class="animate-spin h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Processando...</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                            Aguarde enquanto processamos suas informações.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alpine.js Form Logic -->
    <script>
        function seiForm() {
            return {
                currentStep: 0,
                isLoading: false,
                showSuccessModal: false,
                errors: [],

                steps: [
                    { title: 'Requisitante', key: 'requisitante' },
                    { title: 'Unidade', key: 'unidade' },
                    { title: 'Interessado', key: 'interessado' },
                    { title: 'Localização', key: 'localizacao' },
                    { title: 'Assunto', key: 'assunto' },
                    { title: 'Serviço', key: 'servico' },
                    { title: 'Local', key: 'local' },
                    { title: 'Disciplina', key: 'disciplina' },
                    { title: 'Revisão', key: 'revisao' },
                    { title: 'Número SEI', key: 'seiNumber' },
                    { title: 'Revisão Final', key: 'review' }
                ],

                formData: {
                    requisitante: {
                        nome: '',
                        email: '',
                        email_confirm: ''
                    },
                    unidade: {
                        id: '',
                        text: ''
                    },
                    interessado: {
                        id: '',
                        text: ''
                    },
                    localizacao: {
                        id: '',
                        text: ''
                    },
                    assunto: {
                        id: '',
                        text: ''
                    },
                    servico: {
                        codigo: '',
                        tipo_servico: ''
                    },
                    local: {
                        id: '',
                        text: ''
                    },
                    disciplina: {
                        id: '',
                        text: ''
                    },
                    revisao: {
                        value: '',
                        formatted: ''
                    },
                    seiNumber: {
                        numero: ''
                    }
                },

                init() {
                    // Load data from session storage
                    this.loadFromSession();

                    // Initialize Select2 dropdowns
                    this.$nextTick(() => {
                        this.initializeSelect2();
                    });
                },

                validateStep(stepIndex) {
                    this.errors = [];

                    if (stepIndex === 0) {
                        // Validate Requisitante step
                        if (!this.formData.requisitante.nome.trim()) {
                            this.errors.push('Nome é obrigatório');
                        }
                        if (!this.formData.requisitante.email.trim()) {
                            this.errors.push('E-mail é obrigatório');
                        }
                        if (!this.formData.requisitante.email_confirm.trim()) {
                            this.errors.push('Confirmação de e-mail é obrigatória');
                        }
                        if (this.formData.requisitante.email !== this.formData.requisitante.email_confirm) {
                            this.errors.push('E-mails não coincidem');
                        }

                        // Basic email validation
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (this.formData.requisitante.email && !emailRegex.test(this.formData.requisitante.email)) {
                            this.errors.push('E-mail inválido');
                        }
                    } else if (stepIndex === 1) {
                        // Validate Unidade step
                        if (!this.formData.unidade.id) {
                            this.errors.push('Seleção de unidade é obrigatória');
                        }
                    } else if (stepIndex === 2) {
                        // Validate Interessado step
                        if (!this.formData.interessado.id) {
                            this.errors.push('Seleção de interessado é obrigatória');
                        }
                    } else if (stepIndex === 3) {
                        // Validate Localização step
                        if (!this.formData.localizacao.id) {
                            this.errors.push('Seleção de localização é obrigatória');
                        }
                    } else if (stepIndex === 4) {
                        // Validate Assunto step
                        if (!this.formData.assunto.id) {
                            this.errors.push('Seleção de assunto é obrigatória');
                        }
                    } else if (stepIndex === 5) {
                        // Validate Serviço step - validate codigo if provided
                        if (this.formData.servico.codigo && !this.formData.servico.codigo.match(/^\d+$/)) {
                            this.errors.push('Código do serviço deve conter apenas números');
                        }
                    } else if (stepIndex === 6) {
                        // Validate Local step
                        if (!this.formData.local.id) {
                            this.errors.push('Seleção de local é obrigatória');
                        }
                    } else if (stepIndex === 7) {
                        // Validate Disciplina step
                        if (!this.formData.disciplina.id) {
                            this.errors.push('Seleção de disciplina é obrigatória');
                        }
                    } else if (stepIndex === 8) {
                        // Validate Revisão step - optional field
                        if (this.formData.revisao.value) {
                            const revisaoNum = parseInt(this.formData.revisao.value);
                            if (isNaN(revisaoNum) || revisaoNum < 1 || revisaoNum > 99) {
                                this.errors.push('Revisão deve ser um número entre 1 e 99');
                            }
                        }
                        // No error if field is empty - it's optional
                    } else if (stepIndex === 9) {
                        // Validate Número SEI step - optional field
                        if (this.formData.seiNumber.numero.trim()) {
                            if (!this.formData.seiNumber.numero.match(/^\d+$/)) {
                                this.errors.push('Número SEI deve conter apenas números');
                            }
                        }
                        // No error if field is empty - it's optional
                    }

                    return this.errors.length === 0;
                },

                isStepValid(stepIndex) {
                    if (stepIndex === 0) {
                        return this.formData.requisitante.nome.trim() &&
                               this.formData.requisitante.email.trim() &&
                               this.formData.requisitante.email_confirm.trim() &&
                               this.formData.requisitante.email === this.formData.requisitante.email_confirm;
                    } else if (stepIndex === 1) {
                        return !!this.formData.unidade.id;
                    } else if (stepIndex === 2) {
                        return !!this.formData.interessado.id;
                    } else if (stepIndex === 3) {
                        return !!this.formData.localizacao.id;
                    } else if (stepIndex === 4) {
                        return !!this.formData.assunto.id;
                    } else if (stepIndex === 5) {
                        return true; // Serviço fields are optional
                    } else if (stepIndex === 6) {
                        return !!this.formData.local.id;
                    } else if (stepIndex === 7) {
                        return !!this.formData.disciplina.id;
                    } else if (stepIndex === 8) {
                        // Revisão is optional - always valid, but if provided must be valid
                        if (!this.formData.revisao.value) return true; // Empty is valid
                        const revisaoNum = parseInt(this.formData.revisao.value);
                        return !isNaN(revisaoNum) && revisaoNum >= 1 && revisaoNum <= 99;
                    } else if (stepIndex === 9) {
                        // Número SEI is optional - always valid, but if provided must be valid
                        if (!this.formData.seiNumber.numero.trim()) return true; // Empty is valid
                        return this.formData.seiNumber.numero.match(/^\d+$/);
                    }
                    return true;
                },

                nextStep() {
                    if (this.validateStep(this.currentStep)) {
                        if (this.currentStep < 10) { // Now we have 11 steps (0-10)
                            this.currentStep++;
                            this.saveToSession();
                            this.scrollToTop();
                        }
                    }
                },

                previousStep() {
                    if (this.currentStep > 0) {
                        this.currentStep--;
                        this.errors = []; // Clear errors when going back
                        this.saveToSession();
                        this.scrollToTop();
                    }
                },

                navigateToStep(stepIndex) {
                    // Only allow navigation to completed steps or the next step
                    if (stepIndex <= this.currentStep || (stepIndex === this.currentStep + 1 && this.isStepValid(this.currentStep))) {
                        if (stepIndex < this.currentStep || this.validateStep(this.currentStep)) {
                            this.currentStep = stepIndex;
                            this.saveToSession();
                            this.scrollToTop();
                        }
                    }
                },

                scrollToTop() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                },

                saveToSession() {
                    // Save current step and form data to session storage
                    sessionStorage.setItem('seiFormStep', this.currentStep);
                    sessionStorage.setItem('seiFormData', JSON.stringify(this.formData));
                },

                loadFromSession() {
                    // Load step and form data from session storage
                    const savedStep = sessionStorage.getItem('seiFormStep');
                    const savedData = sessionStorage.getItem('seiFormData');

                    if (savedStep !== null) {
                        this.currentStep = parseInt(savedStep);
                    }

                    if (savedData) {
                        try {
                            const parsedData = JSON.parse(savedData);
                            this.formData = { ...this.formData, ...parsedData };

                            // Restore Select2 values after initialization
                            this.$nextTick(() => {
                                this.restoreSelect2Values();
                            });
                        } catch (e) {
                            console.error('Error parsing saved form data:', e);
                        }
                    }
                },

                restoreSelect2Values() {
                    // Restore Select2 selections from saved data
                    if (this.formData.unidade.id && this.formData.unidade.text) {
                        const option = new Option(this.formData.unidade.text, this.formData.unidade.id, true, true);
                        $('#unidade_select').append(option).trigger('change');
                    }

                    if (this.formData.interessado.id && this.formData.interessado.text) {
                        const option = new Option(this.formData.interessado.text, this.formData.interessado.id, true, true);
                        $('#interessado_select').append(option).trigger('change');
                    }

                    if (this.formData.localizacao.id && this.formData.localizacao.text) {
                        const option = new Option(this.formData.localizacao.text, this.formData.localizacao.id, true, true);
                        $('#localizacao_select').append(option).trigger('change');
                    }

                    if (this.formData.assunto.id && this.formData.assunto.text) {
                        const option = new Option(this.formData.assunto.text, this.formData.assunto.id, true, true);
                        $('#assunto_select').append(option).trigger('change');
                    }

                    if (this.formData.local.id && this.formData.local.text) {
                        const option = new Option(this.formData.local.text, this.formData.local.id, true, true);
                        $('#local_select').append(option).trigger('change');
                    }

                    if (this.formData.disciplina.id && this.formData.disciplina.text) {
                        const option = new Option(this.formData.disciplina.text, this.formData.disciplina.id, true, true);
                        $('#disciplina_select').append(option).trigger('change');
                    }
                },

                clearSession() {
                    sessionStorage.removeItem('seiFormStep');
                    sessionStorage.removeItem('seiFormData');
                },

                async submitForm() {
                    if (!this.validateStep(this.currentStep)) {
                        return;
                    }

                    this.isLoading = true;

                    try {
                        // Prepare submission data
                        const submissionData = {
                            requisitante: {
                                nome: this.formData.requisitante.nome,
                                email: this.formData.requisitante.email
                            },
                            unidade_id: this.formData.unidade.id || null,
                            interessado_id: this.formData.interessado.id || null,
                            localizacao_id: this.formData.localizacao.id || null,
                            assunto_id: this.formData.assunto.id || null,
                            servico_codigo: this.formData.servico.codigo || '',
                            servico_tipo: this.formData.servico.tipo_servico || '',
                            local_id: this.formData.local.id || null,
                            disciplina_id: this.formData.disciplina.id || null,
                            doc_revisao: this.formData.revisao.formatted,
                            doc_sei_num: this.formData.seiNumber.numero
                        };

                        const response = await fetch('/sei/api/submit/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': this.getCSRFToken()
                            },
                            body: JSON.stringify(submissionData)
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            this.showSuccessModal = true;
                            this.clearSession();
                        } else {
                            throw new Error(result.message || 'Erro ao enviar formulário');
                        }
                    } catch (error) {
                        console.error('Error submitting form:', error);
                        this.errors = [error.message || 'Erro ao enviar formulário. Tente novamente.'];
                    } finally {
                        this.isLoading = false;
                    }
                },

                closeSuccessModal() {
                    this.showSuccessModal = false;
                    // Reset form
                    this.currentStep = 0;
                    this.formData = {
                        requisitante: { nome: '', email: '', email_confirm: '' },
                        unidade: { id: '', text: '' },
                        interessado: { id: '', text: '' },
                        localizacao: { id: '', text: '' },
                        assunto: { id: '', text: '' },
                        servico: { codigo: '', tipo_servico: '' },
                        local: { id: '', text: '' },
                        disciplina: { id: '', text: '' },
                        revisao: { value: '', formatted: '' },
                        seiNumber: { numero: '' }
                    };
                    // Clear Select2 selections
                    $('#unidade_select').val(null).trigger('change');
                    $('#interessado_select').val(null).trigger('change');
                    $('#localizacao_select').val(null).trigger('change');
                    $('#assunto_select').val(null).trigger('change');
                    $('#local_select').val(null).trigger('change');
                    $('#disciplina_select').val(null).trigger('change');
                },

                getCSRFToken() {
                    // Get CSRF token from Django
                    const cookies = document.cookie.split(';');
                    for (let cookie of cookies) {
                        const [name, value] = cookie.trim().split('=');
                        if (name === 'csrftoken') {
                            return value;
                        }
                    }
                    return '';
                },

                initializeSelect2() {
                    const self = this;

                    // Initialize Select2 for Unidade
                    $('#unidade_select').select2({
                        placeholder: 'Selecione uma unidade ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/unidade/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.unidade.id = e.params.data.id;
                        self.formData.unidade.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.unidade.id = '';
                        self.formData.unidade.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Interessado
                    $('#interessado_select').select2({
                        placeholder: 'Selecione um interessado ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/interessado/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.interessado.id = e.params.data.id;
                        self.formData.interessado.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.interessado.id = '';
                        self.formData.interessado.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Localização
                    $('#localizacao_select').select2({
                        placeholder: 'Selecione uma localização ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/localizacao/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.localizacao.id = e.params.data.id;
                        self.formData.localizacao.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.localizacao.id = '';
                        self.formData.localizacao.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Assunto
                    $('#assunto_select').select2({
                        placeholder: 'Selecione um assunto ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/assunto/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.assunto.id = e.params.data.id;
                        self.formData.assunto.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.assunto.id = '';
                        self.formData.assunto.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Local
                    $('#local_select').select2({
                        placeholder: 'Selecione um local ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/local/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.local.id = e.params.data.id;
                        self.formData.local.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.local.id = '';
                        self.formData.local.text = '';
                        self.saveToSession();
                    });

                    // Initialize Select2 for Disciplina
                    $('#disciplina_select').select2({
                        placeholder: 'Selecione uma disciplina ou digite para buscar',
                        allowClear: true,
                        ajax: {
                            url: '/sei/api/disciplina/',
                            dataType: 'json',
                            delay: 250,
                            data: function (params) {
                                return { q: params.term || '' };
                            },
                            processResults: function (data) {
                                return { results: data.results };
                            },
                            cache: true
                        }
                    }).on('select2:select', function (e) {
                        self.formData.disciplina.id = e.params.data.id;
                        self.formData.disciplina.text = e.params.data.text;
                        self.saveToSession();
                    }).on('select2:clear', function () {
                        self.formData.disciplina.id = '';
                        self.formData.disciplina.text = '';
                        self.saveToSession();
                    });
                },

                validateNumericInput(event) {
                    // Prevent non-numeric characters from being typed
                    const char = String.fromCharCode(event.which);
                    if (!/[0-9]/.test(char)) {
                        event.preventDefault();
                    }
                },

                validateServicoCodigoInput(event) {
                    // Remove any non-numeric characters from the input
                    const value = event.target.value;
                    const numericValue = value.replace(/[^0-9]/g, '');

                    if (value !== numericValue) {
                        this.formData.servico.codigo = numericValue;
                        event.target.value = numericValue;
                    }

                    // Clear any existing validation errors for this field
                    this.errors = this.errors.filter(error =>
                        error !== 'Código do serviço deve conter apenas números'
                    );
                },

                formatRevisao() {
                    // Format revisão as R + zero-padded 2-digit number
                    const value = parseInt(this.formData.revisao.value);
                    if (!isNaN(value) && value >= 1 && value <= 99) {
                        this.formData.revisao.formatted = 'R' + value.toString().padStart(2, '0');
                    } else {
                        this.formData.revisao.formatted = '';
                    }

                    // Clear validation errors for this field
                    this.errors = this.errors.filter(error =>
                        !error.includes('Revisão')
                    );
                },

                validateRevisaoInput(event) {
                    // Only allow numeric input for revisão
                    const char = String.fromCharCode(event.which);
                    if (!/[0-9]/.test(char)) {
                        event.preventDefault();
                    }
                },

                validateSeiNumberInput(event) {
                    // Remove any non-numeric characters from SEI number input
                    const value = event.target.value;
                    const numericValue = value.replace(/[^0-9]/g, '');

                    if (value !== numericValue) {
                        this.formData.seiNumber.numero = numericValue;
                        event.target.value = numericValue;
                    }

                    // Clear validation errors for this field
                    this.errors = this.errors.filter(error =>
                        !error.includes('Número SEI')
                    );
                }
            }
        }
    </script>
</body>
</html>