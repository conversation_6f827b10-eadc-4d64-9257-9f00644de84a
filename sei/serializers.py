from rest_framework import serializers
from .models import Unidade, Interessado, Localizacao, Assunto, Local, Especialidade

class UnidadeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Unidade
        fields = ['codigo', 'unidade']

class InteressadoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Interessado
        fields = ['codigo', 'concessao']

class LocalizacaoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Localizacao
        fields = ['codigo', 'localizacao']

class AssuntoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assunto
        fields = ['codigo', 'doc']

class LocalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Local
        fields = ['codigo', 'localizacao']

class EspecialidadeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Especialidade
        fields =  ['codigo', 'especialidade']