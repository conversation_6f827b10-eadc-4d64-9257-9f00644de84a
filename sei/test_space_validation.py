"""
Test script to verify that spaces are properly handled in text input fields.
This script tests the frontend behavior for space handling in nome and servico_tipo fields.
"""

from django.test import TestCase, Client
from django.urls import reverse
import json


class SpaceValidationTest(TestCase):
    """Test cases for space handling in text input fields"""
    
    def setUp(self):
        self.client = Client()
        self.submit_url = reverse('sei:form_submit')
        
        # Base valid data for testing
        self.base_data = {
            'requisitante': {
                'nome': '',  # Will be set in individual tests
                'email': '<EMAIL>'
            },
            'unidade_id': None,
            'interessado_id': None,
            'localizacao_id': None,
            'assunto_id': None,
            'servico_codigo': '',
            'servico_tipo': '',  # Will be set in individual tests
            'local_id': None,
            'disciplina_id': None,
            'doc_revisao': '',
            'doc_sei_num': ''
        }
    
    def test_nome_with_internal_spaces_success(self):
        """Test that nome field accepts internal spaces"""
        test_cases = [
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            'A B C',  # Single letters with spaces
            'Nome Com Muitos Espaços Entre Palavras'
        ]
        
        for nome in test_cases:
            with self.subTest(nome=nome):
                data = self.base_data.copy()
                data['requisitante']['nome'] = nome
                
                response = self.client.post(
                    self.submit_url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                # Should not fail due to space validation
                if response.status_code == 400:
                    error_message = response.json().get('message', '')
                    # Should not contain space-related errors
                    self.assertNotIn('espaço', error_message.lower())
                    self.assertNotIn('space', error_message.lower())
                    # May fail for other reasons (missing required fields), but not spaces
    
    def test_servico_tipo_with_internal_spaces_success(self):
        """Test that servico_tipo field accepts internal spaces"""
        test_cases = [
            'Análise Técnica',
            'Consultoria Especializada',
            'Projeto de Engenharia',
            'Estudo de Viabilidade Técnica',
            'Análise de Impacto Ambiental',
            'A B C',  # Single letters with spaces
            ''  # Empty is valid (optional field)
        ]
        
        for servico_tipo in test_cases:
            with self.subTest(servico_tipo=servico_tipo):
                data = self.base_data.copy()
                data['requisitante']['nome'] = 'João Silva'  # Valid nome
                data['servico_tipo'] = servico_tipo
                
                response = self.client.post(
                    self.submit_url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                # Should not fail due to space validation
                if response.status_code == 400:
                    error_message = response.json().get('message', '')
                    # Should not contain space-related errors
                    self.assertNotIn('espaço', error_message.lower())
                    self.assertNotIn('space', error_message.lower())
                    # May fail for other reasons (missing required fields), but not spaces
    
    def test_nome_trimming_behavior(self):
        """Test that nome field properly trims leading/trailing spaces"""
        test_cases = [
            ('  João Silva  ', 'JOÃO SILVA'),  # Leading and trailing spaces
            (' Maria Santos ', 'MARIA SANTOS'),  # Single leading/trailing spaces
            ('Ana Paula   ', 'ANA PAULA'),  # Only trailing spaces
            ('   José Silva', 'JOSÉ SILVA'),  # Only leading spaces
        ]
        
        for input_nome, expected_nome in test_cases:
            with self.subTest(input_nome=repr(input_nome)):
                data = self.base_data.copy()
                data['requisitante']['nome'] = input_nome
                
                response = self.client.post(
                    self.submit_url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                # If successful, check that the name was properly formatted
                if response.status_code == 201:
                    # Get the created protocol to verify formatting
                    result = response.json()
                    protocolo_id = result['protocolo_id']
                    
                    # Check success page to see formatted name
                    success_url = reverse('sei:success', kwargs={'protocolo_id': protocolo_id})
                    success_response = self.client.get(success_url)
                    
                    if success_response.status_code == 200:
                        self.assertContains(success_response, expected_nome)
    
    def test_servico_tipo_trimming_behavior(self):
        """Test that servico_tipo field properly trims leading/trailing spaces"""
        test_cases = [
            ('  Análise Técnica  ', 'ANÁLISE TÉCNICA'),
            (' Consultoria ', 'CONSULTORIA'),
            ('Projeto   ', 'PROJETO'),
            ('   Estudo', 'ESTUDO'),
        ]
        
        for input_tipo, expected_tipo in test_cases:
            with self.subTest(input_tipo=repr(input_tipo)):
                data = self.base_data.copy()
                data['requisitante']['nome'] = 'João Silva'  # Valid nome
                data['servico_tipo'] = input_tipo
                
                response = self.client.post(
                    self.submit_url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                # If successful, check that the type was properly formatted
                if response.status_code == 201:
                    # Get the created protocol to verify formatting
                    result = response.json()
                    protocolo_id = result['protocolo_id']
                    
                    # Check success page to see formatted service type
                    success_url = reverse('sei:success', kwargs={'protocolo_id': protocolo_id})
                    success_response = self.client.get(success_url)
                    
                    if success_response.status_code == 200:
                        self.assertContains(success_response, expected_tipo)
    
    def test_fields_that_should_not_allow_spaces(self):
        """Test that fields like email and codes still block spaces"""
        # Test email with spaces
        data = self.base_data.copy()
        data['requisitante']['nome'] = 'João Silva'
        data['requisitante']['email'] = 'user @example.com'  # Space in email
        
        response = self.client.post(
            self.submit_url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        error_message = response.json().get('message', '')
        self.assertIn('espaço', error_message.lower())
        
        # Test servico_codigo with spaces
        data = self.base_data.copy()
        data['requisitante']['nome'] = 'João Silva'
        data['requisitante']['email'] = '<EMAIL>'
        data['servico_codigo'] = '12 34'  # Space in code
        
        response = self.client.post(
            self.submit_url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        error_message = response.json().get('message', '')
        self.assertIn('números', error_message.lower())


class FrontendSpaceHandlingTest(TestCase):
    """Test cases for frontend space handling behavior"""
    
    def test_javascript_validation_logic(self):
        """Test the JavaScript validation logic for spaces"""
        # This test documents the expected behavior
        # In practice, this would be tested with Selenium or similar
        
        test_scenarios = [
            {
                'field': 'nome',
                'input': 'João Silva',
                'expected_behavior': 'Should allow typing naturally with spaces',
                'expected_validation': 'Valid - 10 characters'
            },
            {
                'field': 'servico_tipo',
                'input': 'Análise Técnica',
                'expected_behavior': 'Should allow typing naturally with spaces',
                'expected_validation': 'Valid - 15 characters'
            },
            {
                'field': 'nome',
                'input': '  João Silva  ',
                'expected_behavior': 'Should allow typing but trim for validation',
                'expected_validation': 'Valid - 10 characters (trimmed)'
            }
        ]
        
        for scenario in test_scenarios:
            # This is a documentation test - actual testing would require browser automation
            self.assertTrue(True, f"Scenario: {scenario}")


if __name__ == '__main__':
    print("Testing space handling in SEI form fields...")
    print("Run with: python manage.py test sei.test_space_validation")
