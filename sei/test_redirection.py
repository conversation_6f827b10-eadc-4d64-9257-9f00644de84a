"""
Test script to verify the SEI form submission and redirection flow.
This script tests the complete user journey from form submission to success page.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
import json
import uuid


class SeiRedirectionTest(TestCase):
    """Test cases for SEI form submission and redirection"""
    
    def setUp(self):
        self.client = Client()
        self.submit_url = reverse('sei:form_submit')
        
        # Valid form data for testing
        self.valid_data = {
            'requisitante': {
                'nome': '<PERSON>',
                'email': '<EMAIL>'
            },
            'unidade_id': None,
            'interessado_id': None,
            'localizacao_id': None,
            'assunto_id': None,
            'servico_codigo': '123',
            'servico_tipo': '<PERSON><PERSON><PERSON><PERSON>',
            'local_id': None,
            'disciplina_id': None,
            'doc_revisao': '',
            'doc_sei_num': ''
        }
    
    def test_successful_submission_returns_protocolo_id(self):
        """Test that successful form submission returns a valid protocolo_id"""
        response = self.client.post(
            self.submit_url,
            data=json.dumps(self.valid_data),
            content_type='application/json'
        )
        
        # Should return 201 for successful creation
        self.assertEqual(response.status_code, 201)
        
        # Parse response
        result = response.json()
        
        # Verify response structure
        self.assertTrue(result.get('success'))
        self.assertIn('protocolo_id', result)
        self.assertIn('doc_cod', result)
        self.assertIn('message', result)
        
        # Verify protocolo_id is a valid UUID
        protocolo_id = result['protocolo_id']
        try:
            uuid.UUID(protocolo_id)
        except ValueError:
            self.fail(f"protocolo_id '{protocolo_id}' is not a valid UUID")
        
        return protocolo_id
    
    def test_success_page_loads_with_valid_protocolo_id(self):
        """Test that the success page loads correctly with a valid protocolo_id"""
        # First, create a protocol
        response = self.client.post(
            self.submit_url,
            data=json.dumps(self.valid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        protocolo_id = response.json()['protocolo_id']
        
        # Now test the success page
        success_url = reverse('sei:success', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(success_url)
        
        # Should load successfully
        self.assertEqual(response.status_code, 200)
        
        # Verify the template is correct
        self.assertContains(response, 'Protocolo Criado com Sucesso!')
        self.assertContains(response, 'joão<EMAIL>')  # Email should be lowercase
        self.assertContains(response, 'JOÃO SILVA SANTOS')  # Name should be uppercase
        
        # Verify action buttons are present
        self.assertContains(response, 'Criar Novo Protocolo')
        self.assertContains(response, 'Imprimir Protocolo')
    
    def test_success_page_redirects_with_invalid_protocolo_id(self):
        """Test that the success page redirects when given an invalid protocolo_id"""
        # Use a random UUID that doesn't exist
        fake_id = str(uuid.uuid4())
        success_url = reverse('sei:success', kwargs={'protocolo_id': fake_id})
        
        response = self.client.get(success_url)
        
        # Should redirect to form page
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('sei:sei_form'))
    
    def test_form_validation_errors_return_400(self):
        """Test that form validation errors return 400 status"""
        # Test with invalid data
        invalid_data = self.valid_data.copy()
        invalid_data['requisitante']['nome'] = 'AB'  # Too short
        
        response = self.client.post(
            self.submit_url,
            data=json.dumps(invalid_data),
            content_type='application/json'
        )
        
        # Should return 400 for validation error
        self.assertEqual(response.status_code, 400)
        
        result = response.json()
        self.assertFalse(result.get('success'))
        self.assertIn('message', result)
        self.assertIn('Nome deve ter pelo menos 3 caracteres', result['message'])
    
    def test_redirection_url_format(self):
        """Test that the redirection URL format is correct"""
        # Create a protocol
        response = self.client.post(
            self.submit_url,
            data=json.dumps(self.valid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        protocolo_id = response.json()['protocolo_id']
        
        # Verify the expected URL format
        expected_url = f'/sei/success/{protocolo_id}/'
        success_url = reverse('sei:success', kwargs={'protocolo_id': protocolo_id})
        
        self.assertEqual(success_url, expected_url)
    
    def test_complete_user_flow(self):
        """Test the complete user flow from submission to success page"""
        # Step 1: Submit form
        response = self.client.post(
            self.submit_url,
            data=json.dumps(self.valid_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        result = response.json()
        
        # Step 2: Verify response contains redirection data
        self.assertTrue(result.get('success'))
        protocolo_id = result['protocolo_id']
        doc_cod = result['doc_cod']
        
        # Step 3: Follow redirection to success page
        success_url = reverse('sei:success', kwargs={'protocolo_id': protocolo_id})
        response = self.client.get(success_url)
        
        # Step 4: Verify success page loads correctly
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, doc_cod)  # Document code should be displayed
        self.assertContains(response, 'JOÃO SILVA SANTOS')  # Formatted name
        self.assertContains(response, '<EMAIL>')  # Formatted email
        
        # Step 5: Verify protocol data is correctly displayed
        context = response.context
        protocolo = context['protocolo']
        self.assertEqual(str(protocolo.id), protocolo_id)
        self.assertEqual(protocolo.doc_cod, doc_cod)
        self.assertEqual(protocolo.usuario.nome, 'JOÃO SILVA SANTOS')
        self.assertEqual(protocolo.usuario.email, '<EMAIL>')


class RedirectionJavaScriptTest(TestCase):
    """Test JavaScript redirection logic (manual verification)"""
    
    def test_uuid_validation_regex(self):
        """Test the UUID validation regex used in JavaScript"""
        import re
        
        # This is the regex used in the JavaScript
        uuid_regex = r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$'
        
        # Test valid UUIDs
        valid_uuids = [
            '123e4567-e89b-12d3-a456-************',
            'f47ac10b-58cc-4372-a567-0e02b2c3d479',
            '6ba7b810-9dad-11d1-80b4-00c04fd430c8'
        ]
        
        for uuid_str in valid_uuids:
            self.assertTrue(re.match(uuid_regex, uuid_str, re.IGNORECASE), 
                          f"Valid UUID {uuid_str} should match regex")
        
        # Test invalid UUIDs
        invalid_uuids = [
            'not-a-uuid',
            '123e4567-e89b-12d3-a456',  # Too short
            '123e4567-e89b-12d3-a456-************-extra',  # Too long
            'gggggggg-gggg-gggg-gggg-gggggggggggg'  # Invalid characters
        ]
        
        for uuid_str in invalid_uuids:
            self.assertFalse(re.match(uuid_regex, uuid_str, re.IGNORECASE), 
                           f"Invalid UUID {uuid_str} should not match regex")


if __name__ == '__main__':
    print("Testing SEI form submission and redirection flow...")
    print("Run with: python manage.py test sei.test_redirection")
