from django.contrib import admin
from .models import (
    Requisitante, Unidade, Interessado, Localizacao, Assunto,
    Servico, Local, Especialidade
)

@admin.register(Requisitante)
class RequisitanteAdmin(admin.ModelAdmin):
    list_display = ('nome', 'email', 'ip_address', 'created_at')
    search_fields = ('nome', 'email')
    list_filter = ('created_at',)

@admin.register(Unidade)
class UnidadeAdmin(admin.ModelAdmin):
    list_display = ('unidade', 'codigo', 'created_at', 'updated_at')
    search_fields = ('unidade', 'codigo')

@admin.register(Interessado)
class InteressadoAdmin(admin.ModelAdmin):
    list_display = ('codigo', 'concessao', 'created_at', 'updated_at')
    search_fields = ('codigo', 'concessao')

@admin.register(Localizacao)
class LocalizacaoAdmin(admin.ModelAdmin):
    list_display = ('localizacao', 'codigo', 'created_at', 'updated_at')
    search_fields = ('localizacao', 'codigo')

@admin.register(Assunto)
class AssuntoAdmin(admin.ModelAdmin):
    list_display = ('descricao', 'codigo', 'created_at', 'updated_at')
    search_fields = ('descricao', 'codigo')

@admin.register(Servico)
class ServicoAdmin(admin.ModelAdmin):
    list_display = ('tipo_servico', 'codigo', 'created_at', 'updated_at')
    search_fields = ('tipo_servico', 'codigo')

@admin.register(Local)
class LocalAdmin(admin.ModelAdmin):
    list_display = ('local', 'codigo', 'created_at', 'updated_at')
    search_fields = ('local', 'codigo')

@admin.register(Especialidade)
class EspecialidadeAdmin(admin.ModelAdmin):
    list_display = ('especialidade', 'codigo', 'created_at', 'updated_at')
    search_fields = ('especialidade', 'codigo')