<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protocolo Criado com Sucesso - Sistema SEI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF161F',
                        'primary-light': '#FF4A52',
                        'primary-dark': '#E6141C',
                        accent: '#FFA500',
                        'accent-light': '#FFB733',
                        'accent-dark': '#E69500'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-black text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold">GERPRO - GERADOR DE PROTOCOLO</h1>
                </div>
                <div class="text-sm">
                    <span class="opacity-90">Protocolo Criado com Sucesso</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Message -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="text-xl font-bold text-green-800">Protocolo Criado com Sucesso!</h2>
                    <p class="text-green-700 mt-2">
                        Seu protocolo foi registrado no sistema. Todas as informações foram enviadas para o seu e-mail 
                        <strong>{{ protocolo.usuario.email }}</strong> juntamente com o código do documento gerado.
                    </p>
                </div>
            </div>
        </div>

        <!-- Protocol Information -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">📋</span>
                Informações do Protocolo
            </h3>

            <!-- Document Code Highlight -->
            <div class="bg-primary-light bg-opacity-10 border border-primary-light rounded-lg p-4 mb-6">
                <div class="text-center">
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Código do Documento</h4>
                    <div class="text-3xl font-mono font-bold text-primary">{{ protocolo.doc_cod }}</div>
                    <p class="text-sm text-gray-600 mt-2">Guarde este código para futuras consultas</p>
                </div>
            </div>

            <!-- Protocol Details Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Requisitante Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Requisitante</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Nome:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.usuario.nome }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">E-mail:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.usuario.email }}</span>
                        </div>
                    </div>
                </div>

                <!-- Unidade Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Unidade</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Unidade:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.unidade.unidade|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.unidade.codigo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Interessado Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Interessado</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.interessado.codigo|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Concessão:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.interessado.concessao|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Localização Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Localização</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Localização:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.localizacao.localizacao|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.localizacao.codigo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Assunto Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Assunto</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Descrição:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.assunto.descricao|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.assunto.codigo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Serviço Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Serviço</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.servico_codigo|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Tipo:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.servico_tipo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Local Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Local</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Local:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.local.local|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.local.codigo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Disciplina Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Disciplina</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Disciplina:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.disciplina.disciplina|default:"Não informado" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Código:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.disciplina.codigo|default:"Não informado" }}</span>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">Informações Adicionais</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Revisão:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.doc_revisao|default:"Não aplicável" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Número SEI:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.doc_sei_num|default:"Não aplicável" }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Data de Criação:</span>
                            <span class="ml-2 text-gray-900">{{ protocolo.created_at|date:"d/m/Y H:i" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'sei:sei_form' %}"
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Criar Novo Protocolo
            </a>

            <a href="{% url 'sei:protocolo_pdf' protocolo_id=protocolo.id %}"
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download PDF
            </a>

            <button onclick="window.print()"
                    class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Imprimir Protocolo
            </button>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-300">
                    © 2025 Sistema GERPRO - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>

    <!-- Print Styles -->
    <style>
        @media print {
            body { background: white !important; }
            header, footer, .no-print { display: none !important; }
            .bg-gray-50, .bg-gray-100 { background: white !important; }
            .shadow-lg { box-shadow: none !important; }
        }
    </style>
</body>
</html>
