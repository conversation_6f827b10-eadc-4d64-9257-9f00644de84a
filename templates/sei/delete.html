<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excluir Protocolo {{ protocolo.doc_cod }} - Sistema SEI</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF161F',
                        'primary-dark': '#E6141B',
                        accent: '#FF6B35',
                        'accent-dark': '#E6602F'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">Sistema SEI</h1>
                    <span class="ml-4 text-gray-500">Excluir Protocolo</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'sei:protocolo_list' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Voltar à Lista
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Warning Message -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-red-800">
                        Atenção: Exclusão de Protocolo
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>Esta ação não pode ser desfeita. O protocolo será permanentemente removido do sistema.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Protocol Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
            <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Protocolo a ser excluído</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary font-mono tracking-wider mb-2">
                            {{ protocolo.doc_cod }}
                        </div>
                        <div class="text-sm text-gray-500">Código do Documento</div>
                    </div>
                    
                    <div class="border-t border-gray-200 pt-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Requisitante</label>
                                <div class="text-gray-900">{{ protocolo.usuario.nome|default:"Não informado" }}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">E-mail</label>
                                <div class="text-gray-900">{{ protocolo.usuario.email|default:"Não informado" }}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data de Criação</label>
                                <div class="text-gray-900">{{ protocolo.created_at|date:"d/m/Y H:i" }}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Tipo de Serviço</label>
                                <div class="text-gray-900">{{ protocolo.servico_tipo|default:"Não informado" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirmar Exclusão</h3>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Consequências da exclusão:
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>O protocolo será removido permanentemente do sistema</li>
                                    <li>Todos os dados associados serão perdidos</li>
                                    <li>Esta ação não pode ser desfeita</li>
                                    <li>O código do documento não poderá ser reutilizado</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <div>
                        <label for="confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                            Para confirmar a exclusão, digite o código do documento: <strong>{{ protocolo.doc_cod }}</strong>
                        </label>
                        <input type="text" 
                               id="confirmation" 
                               name="confirmation" 
                               required
                               placeholder="Digite o código do documento"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
                        <p class="mt-1 text-sm text-gray-500">
                            Esta verificação ajuda a prevenir exclusões acidentais.
                        </p>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-4 justify-end">
                        <a href="{% url 'sei:protocolo_detail' protocolo_id=protocolo.id %}" 
                           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancelar
                        </a>
                        
                        <button type="submit" 
                                class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Confirmar Exclusão
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-300">
                    © 2025 Sistema SEI - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for form validation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const confirmationInput = document.getElementById('confirmation');
            const submitButton = form.querySelector('button[type="submit"]');
            const expectedCode = '{{ protocolo.doc_cod }}';

            function validateConfirmation() {
                const isValid = confirmationInput.value.trim() === expectedCode;
                submitButton.disabled = !isValid;
                
                if (isValid) {
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    submitButton.classList.add('bg-red-600', 'hover:bg-red-700');
                } else {
                    submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                    submitButton.classList.remove('bg-red-600', 'hover:bg-red-700');
                }
            }

            // Initial validation
            validateConfirmation();

            // Validate on input
            confirmationInput.addEventListener('input', validateConfirmation);

            // Prevent form submission if validation fails
            form.addEventListener('submit', function(e) {
                if (confirmationInput.value.trim() !== expectedCode) {
                    e.preventDefault();
                    alert('Por favor, digite o código do documento corretamente para confirmar a exclusão.');
                    confirmationInput.focus();
                }
            });
        });
    </script>
</body>
</html>
