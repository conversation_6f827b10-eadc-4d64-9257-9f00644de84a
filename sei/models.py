import uuid
from django.db import models
from django.core.validators import EmailValidator, RegexValidator
from django.core.exceptions import ValidationError


def validate_numeric_string(value):
    """
    Validator to ensure the value contains only numeric characters.
    The value is stored as a string but must contain only numbers.
    """
    if value and not value.isdigit():
        raise ValidationError(
            'Este campo deve conter apenas números.',
            code='invalid_numeric'
        )


# -----------------------------------------------------------------------------
# Protocolo - main model
# -----------------------------------------------------------------------------
class Protocolo(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    usuario = models.ForeignKey(
        "Requisitante",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Requisitante"
    )
    unidade = models.ForeignKey(
        "Unidade",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Unidade"
    )
    interessado = models.ForeignKey(
        "Interessado",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Interessado"
    )
    localizacao = models.ForeignKey(
        "Localizacao",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Localização"
    )
    assunto = models.ForeignKey(
        "Assunto",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Assunto"
    )
    servico_codigo = models.CharField(
        "Código do Serviço",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        validators=[validate_numeric_string],
        help_text="Código do serviço (apenas números)"
    )
    servico_tipo = models.CharField(
        "Tipo de Serviço",
        max_length=255,
        blank=True,
        null=True,
        help_text="Descrição do tipo de serviço"
    )
    local = models.ForeignKey(
        "Local",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Local"
    )
    disciplina = models.ForeignKey(
        "Disciplina",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="Disciplina"
    )
    doc_cod = models.CharField(
        "Código do Documento",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código do documento"
    )
    doc_url_cod = models.CharField(
        "Código da URL",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código da URL"
    )
    doc_revisao = models.CharField(
        "Revisão do Documento",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Revisão do documento"
    )
    doc_sei_num = models.CharField(
        "Nro. Processo SEI",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Número do processo SEI"
    )
    url = models.URLField(
        verbose_name="URL",
        max_length=200,
        null=True,
        blank=True,
    )
    hash = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name="Hash de Verificação",
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Criado em",
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Atualizado em",
    )

# -----------------------------------------------------------------------------
# Bloco 00: User Information
# -----------------------------------------------------------------------------

class Requisitante(models.Model):
    """
    Represents a user in the system.
    """
    nome = models.CharField(
        max_length=150,
        verbose_name="Nome",
        help_text="Nome completo do requisitante."
    )
    email = models.EmailField(
        validators=[EmailValidator(message="Entre com um endereço de e-mail válido.")],
        verbose_name="E-mail",
        help_text="Endereço de e-mail do requisitante."
    )
    ip_address = models.GenericIPAddressField(
        verbose_name="Endereço IP",
        protocol='both',
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = "Requisitante"
        verbose_name_plural = "Requisitantes"
        ordering = ['nome',] # Order by name, then by most recent

    def __str__(self):
        """
        Returns the string representation of the model, which is the user's name.
        """
        return self.nome


# -----------------------------------------------------------------------------
# Bloco 01: Unidade
# -----------------------------------------------------------------------------

class Unidade(models.Model):
    """Represents a unit or department."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    codigo = models.CharField(
        "Código",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código da unidade"
    )
    unidade = models.CharField(
        "Unidade",
        max_length=255,
        blank=True,
        null=True,
        help_text="Nome da unidade"
    )
    created_at = models.DateTimeField("Criado em", auto_now_add=True)
    updated_at = models.DateTimeField("Atualizado em", auto_now=True)

    class Meta:
        verbose_name = "Unidade"
        verbose_name_plural = "Unidades"
        ordering = ['unidade']

    def __str__(self):
        return f'{self.unidade} - {self.codigo}'

# -----------------------------------------------------------------------------
# Bloco 02: Interessado
# -----------------------------------------------------------------------------

class Interessado(models.Model):
    """Represents an interested party or stakeholder."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    codigo = models.CharField(
        "Código",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código do interessado"
    )
    concessao = models.CharField(
        "Concessão",
        max_length=255,
        blank=True,
        null=True,
        help_text="Informações sobre a concessão"
    )
    sugestao = models.TextField("Sugestão", blank=True, null=True)
    comentarios = models.TextField("Comentários", blank=True, null=True)
    created_at = models.DateTimeField("Criado em", auto_now_add=True)
    updated_at = models.DateTimeField("Atualizado em", auto_now=True)

    class Meta:
        verbose_name = "Interessado"
        verbose_name_plural = "Interessados"
        ordering = ['codigo']

    def __str__(self):
        return self.codigo

# -----------------------------------------------------------------------------
# Bloco 03: Localizacao
# -----------------------------------------------------------------------------

class Localizacao(models.Model):
    """Represents a physical or geographical location."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    codigo = models.CharField(
        "Código",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código da localização"
    )
    localizacao = models.CharField(
        "Localização",
        max_length=255,
        blank=True,
        null=True,
        help_text="Descrição da localização"
    )
    obs = models.CharField("Observação", max_length=255, blank=True, null=True)
    sugestao = models.CharField("Sugestão", max_length=255, blank=True, null=True)
    comentarios = models.CharField("Comentários", max_length=255, blank=True, null=True)
    codificacao = models.CharField("Codificação", max_length=255, blank=True, null=True)
    legenda = models.CharField("Legenda", max_length=255, blank=True, null=True)
    created_at = models.DateTimeField("Criado em", auto_now_add=True)
    updated_at = models.DateTimeField("Atualizado em", auto_now=True)

    class Meta:
        verbose_name = "Localização"
        verbose_name_plural = "Localizações"
        ordering = ['localizacao']

    def __str__(self):
        return self.localizacao or self.codigo

# -----------------------------------------------------------------------------
# Bloco 04: Assunto
# -----------------------------------------------------------------------------

class Assunto(models.Model):
    """Represents a subject or topic."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    codigo = models.CharField(
        "Código",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código do assunto"
    )
    doc = models.CharField("Documento", max_length=255, blank=True, null=True)
    sugestao = models.CharField("Sugestão", max_length=255, blank=True, null=True)
    obs1 = models.CharField("Observação 1", max_length=255, blank=True, null=True)
    obs2 = models.CharField("Observação 2", max_length=255, blank=True, null=True)
    obs3 = models.CharField("Observação 3", max_length=255, blank=True, null=True)
    sigla_padronizada = models.CharField("Sigla Padronizada", max_length=255, blank=True, null=True)
    sigla_original = models.CharField("Sigla Original", max_length=255, blank=True, null=True)
    descricao = models.CharField("Descrição", max_length=255, blank=True, null=True)
    categoria_tematica = models.CharField("Categoria Temática", max_length=255, blank=True, null=True)
    sugestao1 = models.CharField("Sugestão 1", max_length=255, blank=True, null=True)
    extra = models.CharField("Extra", max_length=255, blank=True, null=True)
    created_at = models.DateTimeField("Criado em", auto_now_add=True)
    updated_at = models.DateTimeField("Atualizado em", auto_now=True)

    class Meta:
        verbose_name = "Assunto"
        verbose_name_plural = "Assuntos"
        ordering = ['descricao']

    def __str__(self):
        return self.descricao or self.codigo

# -----------------------------------------------------------------------------
# Bloco 06: Local
# -----------------------------------------------------------------------------

class Local(models.Model):
    """Represents a specific place or venue."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    codigo = models.CharField(
        "Código",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código do local"
    )
    local = models.CharField(
        "Localização",
        max_length=255,
        blank=True,
        null=True,
        help_text="Descrição da localização"
    )
    sugestao = models.CharField("Sugestão", max_length=255, blank=True, null=True)
    comentarios = models.CharField("Comentários", max_length=255, blank=True, null=True)
    created_at = models.DateTimeField("Criado em", auto_now_add=True)
    updated_at = models.DateTimeField("Atualizado em", auto_now=True)

    class Meta:
        verbose_name = "Local"
        verbose_name_plural = "Locais"
        ordering = ['local']

    def __str__(self):
        return self.local or self.codigo

# -----------------------------------------------------------------------------
# Bloco 07: Disciplina
# -----------------------------------------------------------------------------

class Disciplina(models.Model):
    """Represents a specialty or area of expertise."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    codigo = models.CharField(
        "Código",
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Código da disciplina"
    )
    disciplina = models.CharField(
        "Disciplina",
        max_length=255,
        blank=True,
        null=True,
        help_text="Descrição da disciplina"
    )
    sugestao = models.CharField("Sugestão", max_length=255, blank=True, null=True)
    comentarios = models.CharField("Comentários", max_length=255, blank=True, null=True)
    extra = models.CharField("Extra", max_length=255, blank=True, null=True)
    created_at = models.DateTimeField("Criado em", auto_now_add=True)
    updated_at = models.DateTimeField("Atualizado em", auto_now=True)

    class Meta:
        verbose_name = "Disciplina"
        verbose_name_plural = "Disciplinas"
        ordering = ['disciplina']

    def __str__(self):
        return self.disciplina or self.codigo