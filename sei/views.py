from django.views.generic import TemplateView
from django.db.models import Q
from django.db import transaction
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import Unidade, Interessado, Localizacao, Assunto, Local, Disciplina, Requisitante, Protocolo

class SeiFormView(TemplateView):
    template_name = 'sei/form.html'

class UnidadeSearchAPIView(APIView):
    """
    Search Unidade model using __str__() method
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            unidades = Unidade.objects.all()[:10]
        else:
            # Search all fields using the string representation
            unidades = Unidade.objects.filter(
                Q(codigo__icontains=query) |
                Q(unidade__icontains=query)
            ).distinct()[:20]  # Limit to 20 results

        results = []
        for unidade in unidades:
            results.append({
                'id': unidade.id,
                'text': str(unidade)  # Use __str__() method
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class InteressadoSearchAPIView(APIView):
    """
    Search Interessado model by codigo OR concessao fields
    Display only the 'concessao' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            interessados = Interessado.objects.all()[:10]
        else:
            # Search codigo OR concessao fields
            interessados = Interessado.objects.filter(
                Q(codigo__icontains=query) |
                Q(concessao__icontains=query)
            ).distinct()[:20]

        results = []
        for interessado in interessados:
            # Display only the 'concessao' field value
            display_text = interessado.concessao or interessado.codigo or str(interessado)
            results.append({
                'id': interessado.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class LocalizacaoSearchAPIView(APIView):
    """
    Search Localizacao model by codigo OR localizacao fields
    Display only the 'localizacao' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            localizacoes = Localizacao.objects.all()[:10]
        else:
            # Search codigo OR localizacao fields
            localizacoes = Localizacao.objects.filter(
                Q(codigo__icontains=query) |
                Q(localizacao__icontains=query)
            ).distinct()[:20]

        results = []
        for localizacao in localizacoes:
            # Display only the 'localizacao' field value
            display_text = localizacao.localizacao or localizacao.codigo or str(localizacao)
            results.append({
                'id': localizacao.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class AssuntoSearchAPIView(APIView):
    """
    Search Assunto model by codigo OR doc fields
    Display only the 'doc' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            assuntos = Assunto.objects.all()[:10]
        else:
            # Search codigo OR doc fields
            assuntos = Assunto.objects.filter(
                Q(codigo__icontains=query) |
                Q(doc__icontains=query)
            ).distinct()[:20]

        results = []
        for assunto in assuntos:
            # Display only the 'doc' field value
            display_text = assunto.doc or assunto.codigo or str(assunto)
            results.append({
                'id': assunto.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class LocalSearchAPIView(APIView):
    """
    Search Local model by codigo OR local fields
    Display only the 'local' field value to user (note: Local model uses 'local' field, not 'localizacao')
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            locais = Local.objects.all()[:10]
        else:
            # Search codigo OR local fields
            locais = Local.objects.filter(
                Q(codigo__icontains=query) |
                Q(local__icontains=query)
            ).distinct()[:20]

        results = []
        for local_obj in locais:
            # Display only the 'local' field value
            display_text = local_obj.local or local_obj.codigo or str(local_obj)
            results.append({
                'id': local_obj.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class DisciplinaSearchAPIView(APIView):
    """
    Search Disciplina model by codigo OR disciplina fields
    Display only the 'disciplina' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            disciplinas = Disciplina.objects.all()[:10]
        else:
            # Search codigo OR disciplina fields
            disciplinas = Disciplina.objects.filter(
                Q(codigo__icontains=query) |
                Q(disciplina__icontains=query)
            ).distinct()[:20]

        results = []
        for disciplina in disciplinas:
            # Display only the 'disciplina' field value
            display_text = disciplina.disciplina or disciplina.codigo or str(disciplina)
            results.append({
                'id': disciplina.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class SeiFormSubmitAPIView(APIView):
    """
    Handle form submission with transaction support
    """
    @transaction.atomic
    def post(self, request, format=None):
        try:
            data = request.data

            # Get optional fields
            doc_revisao = data.get('doc_revisao', '').strip()
            doc_sei_num = data.get('doc_sei_num', '').strip()

            # Validate SEI number is numeric if provided
            if doc_sei_num and not doc_sei_num.isdigit():
                return Response({
                    'success': False,
                    'message': 'Número SEI deve conter apenas números.'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create Requisitante
            requisitante = Requisitante.objects.create(
                nome=data.get('requisitante', {}).get('nome', ''),
                email=data.get('requisitante', {}).get('email', ''),
                ip_address=self.get_client_ip(request)
            )

            # Get related objects
            unidade = None
            interessado = None
            localizacao = None
            assunto = None
            local = None
            disciplina = None

            if data.get('unidade_id'):
                try:
                    unidade = Unidade.objects.get(id=data.get('unidade_id'))
                except Unidade.DoesNotExist:
                    pass

            if data.get('interessado_id'):
                try:
                    interessado = Interessado.objects.get(id=data.get('interessado_id'))
                except Interessado.DoesNotExist:
                    pass

            if data.get('localizacao_id'):
                try:
                    localizacao = Localizacao.objects.get(id=data.get('localizacao_id'))
                except Localizacao.DoesNotExist:
                    pass

            if data.get('assunto_id'):
                try:
                    assunto = Assunto.objects.get(id=data.get('assunto_id'))
                except Assunto.DoesNotExist:
                    pass

            if data.get('local_id'):
                try:
                    local = Local.objects.get(id=data.get('local_id'))
                except Local.DoesNotExist:
                    pass

            if data.get('disciplina_id'):
                try:
                    disciplina = Disciplina.objects.get(id=data.get('disciplina_id'))
                except Disciplina.DoesNotExist:
                    pass

            # Get service data directly from form submission
            servico_codigo = data.get('servico_codigo', '').strip()
            servico_tipo = data.get('servico_tipo', '').strip()

            # Validate service codigo field - must be numeric if provided
            if servico_codigo and not servico_codigo.isdigit():
                return Response({
                    'success': False,
                    'message': 'O código do serviço deve conter apenas números.'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Generate doc_cod by concatenating codigo values
            doc_cod_parts = []
            doc_cod_parts.append(unidade.codigo if unidade and unidade.codigo else '')
            doc_cod_parts.append(interessado.codigo if interessado and interessado.codigo else '')
            doc_cod_parts.append(localizacao.codigo if localizacao and localizacao.codigo else '')
            doc_cod_parts.append(assunto.codigo if assunto and assunto.codigo else '')
            doc_cod_parts.append(servico_codigo if servico_codigo else '')
            doc_cod_parts.append(local.codigo if local and local.codigo else '')
            doc_cod_parts.append(disciplina.codigo if disciplina and disciplina.codigo else '')
            doc_cod_parts.append(doc_revisao if doc_revisao else '')
            doc_cod_parts.append(doc_sei_num if doc_sei_num else '')

            doc_cod = '-'.join(doc_cod_parts)

            # Create Protocolo object
            protocolo = Protocolo.objects.create(
                usuario=requisitante,
                unidade=unidade,
                interessado=interessado,
                localizacao=localizacao,
                assunto=assunto,
                local=local,
                disciplina=disciplina,
                servico_codigo=servico_codigo,
                servico_tipo=servico_tipo,
                doc_cod=doc_cod,
                doc_revisao=doc_revisao,
                doc_sei_num=doc_sei_num,
                ip_address=self.get_client_ip(request)
            )

            return Response({
                'success': True,
                'message': 'Formulário enviado com sucesso!',
                'protocolo_id': str(protocolo.id),
                'doc_cod': doc_cod
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Erro ao processar formulário: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
