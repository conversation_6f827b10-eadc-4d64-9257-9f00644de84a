Create another page name "pdf.html" that will be like the same as "sucess.html" page, but it will render a pdf like page. Create the endpoint to generate a pdf from that template and another to view the rendered html page. I have create similar function for another app named "protocolo" (see below). I want something similar.

def protocolo_pdf_view(request, protocolo_uid):
    protocolo = get_object_or_404(Protocolo, uid=protocolo_uid)
    template_path = 'protocolo/pdf_template.html'
    context = {
        'protocolo': protocolo,
    }

    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{protocolo.protocolo}.pdf"'

    # Generate the PDF
    pdf = render_to_pdf(template_path, context)
    if pdf:
        response.write(pdf.getvalue())
        return response
    return HttpResponse('Error generating PDF', status=500)


def protocolo_pdf_view_html(request, protocolo_uid):
    protocolo = get_object_or_404(Protocolo, uid=protocolo_uid)
    template_path = 'protocolo/pdf_template.html'
    context = {
        'protocolo': protocolo,
    }

    return render(request, template_path, context)