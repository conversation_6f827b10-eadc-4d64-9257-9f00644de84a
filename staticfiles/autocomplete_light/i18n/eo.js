/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */
var dalLoadLanguage=function(n){var e;(e=n&&n.fn&&n.fn.select2&&n.fn.select2.amd?n.fn.select2.amd:e).define("select2/i18n/eo",[],function(){return{errorLoading:function(){return"La rezultoj ne povas esti ŝargitaj."},inputTooLong:function(n){n=n.input.length-n.maximum;return"Bonvolu forigi "+n+" signo"+(1==n?"n":"jn")},inputTooShort:function(n){return"Bv. enigi "+(n.minimum-n.input.length)+" aŭ pli multajn signojn"},loadingMore:function(){return"Ŝargado de pliaj rezultoj…"},maximumSelected:function(n){var e="Vi povas elekti nur "+n.maximum+" ero";return 1==n.maximum?e+="n":e+="jn",e},noResults:function(){return"Neniuj rezultoj trovitaj"},searching:function(){return"Serĉado…"},removeAllItems:function(){return"Forigi ĉiujn erojn"}}}),e.define,e.require},event=new CustomEvent("dal-language-loaded",{lang:"eo"});document.dispatchEvent(event);