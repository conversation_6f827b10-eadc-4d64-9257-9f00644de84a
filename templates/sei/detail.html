<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protocolo {{ protocolo.doc_cod }} - Sistema SEI</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF161F',
                        'primary-dark': '#E6141B',
                        accent: '#FF6B35',
                        'accent-dark': '#E6602F'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">Sistema SEI</h1>
                    <span class="ml-4 text-gray-500">Detalhes do Protocolo</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'sei:protocolo_list' %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Voltar à Lista
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Message -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-green-800">
                        Protocolo Criado com Sucesso!
                    </h3>
                    <div class="mt-2 text-sm text-green-700">
                        <p>Todas as informações foram enviadas para o seu e-mail: <strong>{{ protocolo.usuario.email }}</strong></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Code Display -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Código do Documento</h2>
            <div class="text-4xl font-bold text-primary font-mono tracking-wider">
                {{ protocolo.doc_cod }}
            </div>
        </div>

        <!-- Protocol Information -->
        <div class="space-y-6">
            <!-- Requisitante Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">1. Dados do Requisitante</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Nome Completo</label>
                            <div class="text-lg text-gray-900">{{ protocolo.usuario.nome|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">E-mail</label>
                            <div class="text-lg text-gray-900">{{ protocolo.usuario.email|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Unidade Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">2. Unidade</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Unidade</label>
                            <div class="text-lg text-gray-900">{{ protocolo.unidade.unidade|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Código</label>
                            <div class="text-lg text-gray-900">{{ protocolo.unidade.codigo|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interessado Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">3. Interessado</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Código</label>
                            <div class="text-lg text-gray-900">{{ protocolo.interessado.codigo|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Concessão</label>
                            <div class="text-lg text-gray-900">{{ protocolo.interessado.concessao|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Localização Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">4. Localização</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Localização</label>
                            <div class="text-lg text-gray-900">{{ protocolo.localizacao.localizacao|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Código</label>
                            <div class="text-lg text-gray-900">{{ protocolo.localizacao.codigo|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assunto Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">5. Assunto</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
                            <div class="text-lg text-gray-900">{{ protocolo.assunto.descricao|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Código</label>
                            <div class="text-lg text-gray-900">{{ protocolo.assunto.codigo|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Serviço Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">6. Serviço</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Código do Serviço</label>
                            <div class="text-lg text-gray-900">{{ protocolo.servico_codigo|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tipo de Serviço</label>
                            <div class="text-lg text-gray-900">{{ protocolo.servico_tipo|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Local Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">7. Local</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Local</label>
                            <div class="text-lg text-gray-900">{{ protocolo.local.local|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Código</label>
                            <div class="text-lg text-gray-900">{{ protocolo.local.codigo|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Disciplina Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">8. Disciplina</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Disciplina</label>
                            <div class="text-lg text-gray-900">{{ protocolo.disciplina.disciplina|default:"Não informado" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Código</label>
                            <div class="text-lg text-gray-900">{{ protocolo.disciplina.codigo|default:"Não informado" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-primary px-6 py-3">
                    <h3 class="text-lg font-semibold text-white">9. Informações Adicionais</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Revisão</label>
                            <div class="text-lg text-gray-900">{{ protocolo.doc_revisao|default:"Não aplicável" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Número SEI</label>
                            <div class="text-lg text-gray-900">{{ protocolo.doc_sei_num|default:"Não aplicável" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Data de Criação</label>
                            <div class="text-lg text-gray-900">{{ protocolo.created_at|date:"d/m/Y H:i" }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Última Atualização</label>
                            <div class="text-lg text-gray-900">{{ protocolo.updated_at|date:"d/m/Y H:i" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
            <a href="{% url 'sei:protocolo_list' %}" 
               class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Voltar à Lista
            </a>
            
            <a href="{% url 'sei:protocolo_edit' protocolo_id=protocolo.id %}" 
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Editar Protocolo
            </a>
            
            <a href="{% url 'sei:protocolo_pdf' protocolo_id=protocolo.id %}" 
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download PDF
            </a>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-300">
                    © 2025 Sistema SEI - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
