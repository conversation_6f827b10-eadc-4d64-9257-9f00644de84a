from django.urls import path
from . import views

app_name = "sei"

urlpatterns = [
    path('', views.SeiFormView.as_view(), name='sei_form'),
    path('api/unidade/codigo/', views.UnidadeListByCodigo.as_view(), name='unidade_codigo'),
    path('api/unidade/nome/', views.UnidadeListByNome.as_view(), name='unidade_nome'),
    path('api/unidade/', views.UnidadeListByCodigoOrNome.as_view(), name='unidade_codigo_ou_nome'),
    path('api/interessado/codigo/', views.InteressadoListByCodigo.as_view(), name='interessado_codigo'),
    path('api/interessado/concessao/', views.InteressadoListByConcessao.as_view(), name='interessado_concessao'),
    path('api/interessado/', views.InteressadoListByCodigoOrConcessao.as_view(), name='interessado_codigo_ou_concessao'),
    path('api/localizacao/codigo/', views.LocalizacaoListByCodigo.as_view(), name='localizacao_codigo'),
    path('api/localizacao/nome/', views.LocalizacaoListByNome.as_view(), name='localizacao_nome'),
    path('api/localizacao/', views.LocalizacaoListByCodigoOrNome.as_view(), name='localizacao_codigo_ou_nome'),
    path('api/assunto/codigo/', views.AssuntoListByCodigo.as_view(), name='assunto_codigo'),
    path('api/assunto/doc/', views.AssuntoListByDoc.as_view(), name='assunto_doc'),
    path('api/assunto/', views.AssuntoListByCodigoOrDoc.as_view(), name='assunto_codigo_ou_doc'),
    path('api/local/codigo/', views.LocalListByCodigo.as_view(), name='local_codigo'),
    path('api/local/localizacao/', views.LocalListByLocalizacao.as_view(), name='local_localizacao'),
    path('api/local/', views.LocalListByCodigoOrLocalizacao.as_view(), name='local_codigo_ou_localizacao'),
    path('api/especialidade/codigo/', views.EspecialidadeListByCodigo.as_view(), name='especialidade_codigo'),
    path('api/especialidade/nome/', views.EspecialidadeListByNome.as_view(), name='especialidade_nome'),
    path('api/especialidade/', views.EspecialidadeListByCodigoOrNome.as_view(), name='especialidade_codigo_ou_nome'),
]