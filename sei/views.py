from django.views.generic import TemplateView
from django.db.models import Q
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import Unidade, Interessado, Localizacao, Assunto, Local, Especialidade, Requisitante, Servico

class SeiFormView(TemplateView):
    template_name = 'sei/form.html'

class UnidadeSearchAPIView(APIView):
    """
    Search Unidade model using __str__() method
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            unidades = Unidade.objects.all()[:10]
        else:
            # Search all fields using the string representation
            unidades = Unidade.objects.filter(
                Q(codigo__icontains=query) |
                Q(unidade__icontains=query)
            ).distinct()[:20]  # Limit to 20 results

        results = []
        for unidade in unidades:
            results.append({
                'id': unidade.id,
                'text': str(unidade)  # Use __str__() method
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class InteressadoSearchAPIView(APIView):
    """
    Search Interessado model by codigo OR concessao fields
    Display only the 'concessao' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            interessados = Interessado.objects.all()[:10]
        else:
            # Search codigo OR concessao fields
            interessados = Interessado.objects.filter(
                Q(codigo__icontains=query) |
                Q(concessao__icontains=query)
            ).distinct()[:20]

        results = []
        for interessado in interessados:
            # Display only the 'concessao' field value
            display_text = interessado.concessao or interessado.codigo or str(interessado)
            results.append({
                'id': interessado.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class LocalizacaoSearchAPIView(APIView):
    """
    Search Localizacao model by codigo OR localizacao fields
    Display only the 'localizacao' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            localizacoes = Localizacao.objects.all()[:10]
        else:
            # Search codigo OR localizacao fields
            localizacoes = Localizacao.objects.filter(
                Q(codigo__icontains=query) |
                Q(localizacao__icontains=query)
            ).distinct()[:20]

        results = []
        for localizacao in localizacoes:
            # Display only the 'localizacao' field value
            display_text = localizacao.localizacao or localizacao.codigo or str(localizacao)
            results.append({
                'id': localizacao.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class AssuntoSearchAPIView(APIView):
    """
    Search Assunto model by codigo OR doc fields
    Display only the 'doc' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            assuntos = Assunto.objects.all()[:10]
        else:
            # Search codigo OR doc fields
            assuntos = Assunto.objects.filter(
                Q(codigo__icontains=query) |
                Q(doc__icontains=query)
            ).distinct()[:20]

        results = []
        for assunto in assuntos:
            # Display only the 'doc' field value
            display_text = assunto.doc or assunto.codigo or str(assunto)
            results.append({
                'id': assunto.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class LocalSearchAPIView(APIView):
    """
    Search Local model by codigo OR local fields
    Display only the 'local' field value to user (note: Local model uses 'local' field, not 'localizacao')
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            locais = Local.objects.all()[:10]
        else:
            # Search codigo OR local fields
            locais = Local.objects.filter(
                Q(codigo__icontains=query) |
                Q(local__icontains=query)
            ).distinct()[:20]

        results = []
        for local_obj in locais:
            # Display only the 'local' field value
            display_text = local_obj.local or local_obj.codigo or str(local_obj)
            results.append({
                'id': local_obj.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class EspecialidadeSearchAPIView(APIView):
    """
    Search Especialidade model by codigo OR especialidade fields
    Display only the 'especialidade' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            especialidades = Especialidade.objects.all()[:10]
        else:
            # Search codigo OR especialidade fields
            especialidades = Especialidade.objects.filter(
                Q(codigo__icontains=query) |
                Q(especialidade__icontains=query)
            ).distinct()[:20]

        results = []
        for especialidade in especialidades:
            # Display only the 'especialidade' field value
            display_text = especialidade.especialidade or especialidade.codigo or str(especialidade)
            results.append({
                'id': especialidade.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class SeiFormSubmitAPIView(APIView):
    """
    Handle form submission
    """
    def post(self, request, format=None):
        try:
            data = request.data

            # Create Requisitante
            requisitante = Requisitante.objects.create(
                nome=data.get('requisitante', {}).get('nome', ''),
                email=data.get('requisitante', {}).get('email', ''),
                ip_address=self.get_client_ip(request)
            )

            # Create Servico if data provided
            servico_data = data.get('servico', {})
            if servico_data.get('codigo') or servico_data.get('tipo_servico'):
                # Validate codigo field - must be numeric if provided
                codigo = servico_data.get('codigo', '').strip()
                if codigo and not codigo.isdigit():
                    return Response({
                        'success': False,
                        'message': 'O código do serviço deve conter apenas números.'
                    }, status=status.HTTP_400_BAD_REQUEST)

                servico = Servico.objects.create(
                    codigo=codigo,
                    tipo_servico=servico_data.get('tipo_servico', ''),
                    comentarios=servico_data.get('comentarios', '')
                )

            return Response({
                'success': True,
                'message': 'Formulário enviado com sucesso!',
                'requisitante_id': requisitante.id
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Erro ao processar formulário: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
