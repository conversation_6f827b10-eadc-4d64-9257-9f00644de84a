from rest_framework import serializers
from .models import <PERSON>quisita<PERSON>, Servico

class RequisitanteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Requisitante
        fields = ['nome', 'email', 'ip_address']

class ServicoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Servico
        fields = ['codigo', 'tipo_servico', 'comentarios']

    def validate_codigo(self, value):
        """
        Validate that codigo contains only numeric characters.
        """
        if value and not value.isdigit():
            raise serializers.ValidationError(
                "O código deve conter apenas números."
            )
        return value

class SeiFormSubmissionSerializer(serializers.Serializer):
    """
    Serializer for the complete SEI form submission
    """
    requisitante = RequisitanteSerializer()
    unidade_id = serializers.UUIDField(required=False, allow_null=True)
    interessado_id = serializers.UUIDField(required=False, allow_null=True)
    localizacao_id = serializers.UUIDField(required=False, allow_null=True)
    assunto_id = serializers.UUIDField(required=False, allow_null=True)
    servico = ServicoSerializer(required=False)
    local_id = serializers.UUIDField(required=False, allow_null=True)
    especialidade_id = serializers.UUIDField(required=False, allow_null=True)