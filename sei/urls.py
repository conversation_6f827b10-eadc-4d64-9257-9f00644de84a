from django.urls import path
from . import views

app_name = "sei"

urlpatterns = [
    # Form view
    path('', views.SeiFormView.as_view(), name='sei_form'),
    path('success/<uuid:protocolo_id>/', views.SeiSuccessView.as_view(), name='success'),

    # API endpoints for Select2 dropdowns
    path('api/unidade/', views.UnidadeSearchAPIView.as_view(), name='unidade_search'),
    path('api/interessado/', views.InteressadoSearchAPIView.as_view(), name='interessado_search'),
    path('api/localizacao/', views.LocalizacaoSearchAPIView.as_view(), name='localizacao_search'),
    path('api/assunto/', views.AssuntoSearchAPIView.as_view(), name='assunto_search'),
    path('api/local/', views.LocalSearchAPIView.as_view(), name='local_search'),
    path('api/disciplina/', views.DisciplinaSearchAPIView.as_view(), name='disciplina_search'),

    # Form submission
    path('api/submit/', views.SeiFormSubmitAPIView.as_view(), name='form_submit'),
]