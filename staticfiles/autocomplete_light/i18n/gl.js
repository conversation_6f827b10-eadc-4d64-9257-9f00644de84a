/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */
var dalLoadLanguage=function(n){var e;(e=n&&n.fn&&n.fn.select2&&n.fn.select2.amd?n.fn.select2.amd:e).define("select2/i18n/gl",[],function(){return{errorLoading:function(){return"Non foi posíbel cargar os resultados."},inputTooLong:function(n){n=n.input.length-n.maximum;return 1==n?"Elimine un carácter":"Elimine "+n+" caracteres"},inputTooShort:function(n){n=n.minimum-n.input.length;return 1==n?"Engada un carácter":"Engada "+n+" caracteres"},loadingMore:function(){return"Cargando máis resultados…"},maximumSelected:function(n){return 1===n.maximum?"Só pode seleccionar un elemento":"Só pode seleccionar "+n.maximum+" elementos"},noResults:function(){return"Non se atoparon resultados"},searching:function(){return"Buscando…"},removeAllItems:function(){return"Elimina todos os elementos"}}}),e.define,e.require},event=new CustomEvent("dal-language-loaded",{lang:"gl"});document.dispatchEvent(event);