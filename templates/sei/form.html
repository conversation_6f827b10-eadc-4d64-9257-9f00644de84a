<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulário SEI - Sistema de Informações</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- jQuery (required for Select2) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#FF161F',
                        'primary-dark': '#E6141C',
                        'primary-light': '#FF3A42',
                        'secondary': '#034EA2',
                        'secondary-dark': '#02407A',
                        'accent': '#0B9247',
                        'accent-dark': '#0A7A3A',
                        'highlight': '#FBB900',
                        'highlight-dark': '#E6A600'
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        /* Select2 Custom Styling */
        .select2-container--default .select2-selection--single {
            height: 42px !important;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem !important;
            padding: 0.5rem !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 26px !important;
            padding-left: 0 !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 40px !important;
            right: 8px !important;
        }

        .select2-container--default.select2-container--focus .select2-selection--single {
            border-color: #FF161F !important;
            box-shadow: 0 0 0 1px #FF161F !important;
        }

        /* Progress Bar Animation */
        .progress-bar {
            transition: width 0.3s ease-in-out;
        }

        /* Step Indicator Animation */
        .step-indicator {
            transition: all 0.3s ease-in-out;
        }

        /* Form Animation */
        .form-step {
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        .form-step.hidden {
            opacity: 0;
            transform: translateX(20px);
        }

        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #FF161F;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #E6141C;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div x-data="seiForm()" class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Sistema de Informações SEI</h1>
            <p class="text-gray-600">Preencha as informações em 8 etapas para completar o cadastro</p>
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">Progresso</span>
                <span class="text-sm font-medium text-primary" x-text="`${Math.round(((currentStep + 1) / 8) * 100)}%`"></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-primary h-2 rounded-full progress-bar"
                     :style="`width: ${((currentStep + 1) / 8) * 100}%`"></div>
            </div>
        </div>

        <!-- Step Indicators -->
        <div class="flex justify-between mb-8 overflow-x-auto custom-scrollbar">
            <template x-for="(step, index) in steps" :key="index">
                <div class="flex flex-col items-center min-w-0 flex-1 px-2">
                    <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium mb-2 cursor-pointer"
                         :class="{
                             'bg-primary text-white': index === currentStep,
                             'bg-green-500 text-white': index < currentStep && isStepValid(index),
                             'bg-gray-300 text-gray-600': index > currentStep,
                             'bg-red-500 text-white': index < currentStep && !isStepValid(index)
                         }"
                         @click="navigateToStep(index)">
                        <span x-show="index < currentStep && isStepValid(index)">✓</span>
                        <span x-show="!(index < currentStep && isStepValid(index))" x-text="index + 1"></span>
                    </div>
                    <span class="text-xs text-center text-gray-600 leading-tight" x-text="step.title"></span>
                </div>
            </template>
        </div>

        <!-- Error Display -->
        <div x-show="errors.length > 0" class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Erro na validação</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <template x-for="error in errors" :key="error">
                                <li x-text="error"></li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Container -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <!-- Step 0: Requisitante (Usuario) -->
            <div x-show="currentStep === 0" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">1</span>
                    Informações do Requisitante
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <label for="nome" class="block text-sm font-medium text-gray-700 mb-2">
                            Nome Completo <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="nome"
                               x-model="formData.requisitante.nome"
                               @input="saveToSession()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               :class="{'border-red-500': errors.includes('Nome é obrigatório')}"
                               placeholder="Digite seu nome completo">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            E-mail <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="email"
                               x-model="formData.requisitante.email"
                               @input="saveToSession()"
                               @paste.prevent
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               :class="{'border-red-500': errors.includes('E-mail é obrigatório') || errors.includes('E-mails não coincidem')}"
                               placeholder="<EMAIL>">
                        <p class="text-xs text-red-500 mt-1">⚠️ Colar não permitido - digite manualmente</p>
                    </div>

                    <div>
                        <label for="email_confirm" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirmar E-mail <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="email_confirm"
                               x-model="formData.requisitante.email_confirm"
                               @input="saveToSession()"
                               @paste.prevent
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               :class="{'border-red-500': errors.includes('Confirmação de e-mail é obrigatória') || errors.includes('E-mails não coincidem')}"
                               placeholder="Confirme seu e-mail">
                        <p class="text-xs text-red-500 mt-1">⚠️ Colar não permitido - digite manualmente</p>
                    </div>
                </div>
            </div>

            <!-- Step 1: Unidade -->
            <div x-show="currentStep === 1" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-secondary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">2</span>
                    Unidade
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="unidade_codigo" class="block text-sm font-medium text-gray-700 mb-2">Código</label>
                        <select id="unidade_codigo"
                                x-model="formData.unidade.codigo"
                                @change="saveToSession()"
                                class="w-full select2-unidade">
                            <option value="">Selecione um código</option>
                        </select>
                    </div>

                    <div>
                        <label for="unidade_unidade" class="block text-sm font-medium text-gray-700 mb-2">Unidade</label>
                        <select id="unidade_unidade"
                                x-model="formData.unidade.unidade"
                                @change="saveToSession()"
                                class="w-full select2-unidade">
                            <option value="">Selecione uma unidade</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 2: Interessado -->
            <div x-show="currentStep === 2" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-accent text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">3</span>
                    Interessado
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="interessado_codigo" class="block text-sm font-medium text-gray-700 mb-2">Código</label>
                        <select id="interessado_codigo"
                                x-model="formData.interessado.codigo"
                                @change="saveToSession()"
                                class="w-full select2-interessado">
                            <option value="">Selecione um código</option>
                        </select>
                    </div>

                    <div>
                        <label for="interessado_concessao" class="block text-sm font-medium text-gray-700 mb-2">Concessão</label>
                        <select id="interessado_concessao"
                                x-model="formData.interessado.concessao"
                                @change="saveToSession()"
                                class="w-full select2-interessado">
                            <option value="">Selecione uma concessão</option>
                        </select>
                    </div>

                    <div>
                        <label for="interessado_sugestao" class="block text-sm font-medium text-gray-700 mb-2">Sugestão</label>
                        <select id="interessado_sugestao"
                                x-model="formData.interessado.sugestao"
                                @change="saveToSession()"
                                class="w-full select2-interessado">
                            <option value="">Selecione uma sugestão</option>
                        </select>
                    </div>

                    <div>
                        <label for="interessado_comentarios" class="block text-sm font-medium text-gray-700 mb-2">Comentários</label>
                        <select id="interessado_comentarios"
                                x-model="formData.interessado.comentarios"
                                @change="saveToSession()"
                                class="w-full select2-interessado">
                            <option value="">Selecione comentários</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 3: Localização -->
            <div x-show="currentStep === 3" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-highlight text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">4</span>
                    Localização
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label for="localizacao_codigo" class="block text-sm font-medium text-gray-700 mb-2">Código</label>
                        <select id="localizacao_codigo"
                                x-model="formData.localizacao.codigo"
                                @change="saveToSession()"
                                class="w-full select2-localizacao">
                            <option value="">Selecione um código</option>
                        </select>
                    </div>

                    <div>
                        <label for="localizacao_localizacao" class="block text-sm font-medium text-gray-700 mb-2">Localização</label>
                        <select id="localizacao_localizacao"
                                x-model="formData.localizacao.localizacao"
                                @change="saveToSession()"
                                class="w-full select2-localizacao">
                            <option value="">Selecione uma localização</option>
                        </select>
                    </div>

                    <div>
                        <label for="localizacao_obs" class="block text-sm font-medium text-gray-700 mb-2">Observação</label>
                        <select id="localizacao_obs"
                                x-model="formData.localizacao.obs"
                                @change="saveToSession()"
                                class="w-full select2-localizacao">
                            <option value="">Selecione uma observação</option>
                        </select>
                    </div>

                    <div>
                        <label for="localizacao_sugestao" class="block text-sm font-medium text-gray-700 mb-2">Sugestão</label>
                        <select id="localizacao_sugestao"
                                x-model="formData.localizacao.sugestao"
                                @change="saveToSession()"
                                class="w-full select2-localizacao">
                            <option value="">Selecione uma sugestão</option>
                        </select>
                    </div>

                    <div>
                        <label for="localizacao_comentarios" class="block text-sm font-medium text-gray-700 mb-2">Comentários</label>
                        <select id="localizacao_comentarios"
                                x-model="formData.localizacao.comentarios"
                                @change="saveToSession()"
                                class="w-full select2-localizacao">
                            <option value="">Selecione comentários</option>
                        </select>
                    </div>

                    <div>
                        <label for="localizacao_codificacao" class="block text-sm font-medium text-gray-700 mb-2">Codificação</label>
                        <select id="localizacao_codificacao"
                                x-model="formData.localizacao.codificacao"
                                @change="saveToSession()"
                                class="w-full select2-localizacao">
                            <option value="">Selecione uma codificação</option>
                        </select>
                    </div>

                    <div class="md:col-span-2 lg:col-span-1">
                        <label for="localizacao_legenda" class="block text-sm font-medium text-gray-700 mb-2">Legenda</label>
                        <select id="localizacao_legenda"
                                x-model="formData.localizacao.legenda"
                                @change="saveToSession()"
                                class="w-full select2-localizacao">
                            <option value="">Selecione uma legenda</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 4: Assunto -->
            <div x-show="currentStep === 4" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">5</span>
                    Assunto
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label for="assunto_codigo" class="block text-sm font-medium text-gray-700 mb-2">Código</label>
                        <select id="assunto_codigo"
                                x-model="formData.assunto.codigo"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione um código</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_doc" class="block text-sm font-medium text-gray-700 mb-2">Documento</label>
                        <select id="assunto_doc"
                                x-model="formData.assunto.doc"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione um documento</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_sugestao" class="block text-sm font-medium text-gray-700 mb-2">Sugestão</label>
                        <select id="assunto_sugestao"
                                x-model="formData.assunto.sugestao"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione uma sugestão</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_obs1" class="block text-sm font-medium text-gray-700 mb-2">Observação 1</label>
                        <select id="assunto_obs1"
                                x-model="formData.assunto.obs1"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione observação 1</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_obs2" class="block text-sm font-medium text-gray-700 mb-2">Observação 2</label>
                        <select id="assunto_obs2"
                                x-model="formData.assunto.obs2"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione observação 2</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_obs3" class="block text-sm font-medium text-gray-700 mb-2">Observação 3</label>
                        <select id="assunto_obs3"
                                x-model="formData.assunto.obs3"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione observação 3</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_sigla_padronizada" class="block text-sm font-medium text-gray-700 mb-2">Sigla Padronizada</label>
                        <select id="assunto_sigla_padronizada"
                                x-model="formData.assunto.sigla_padronizada"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione uma sigla padronizada</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_sigla_original" class="block text-sm font-medium text-gray-700 mb-2">Sigla Original</label>
                        <select id="assunto_sigla_original"
                                x-model="formData.assunto.sigla_original"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione uma sigla original</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_descricao" class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                        <select id="assunto_descricao"
                                x-model="formData.assunto.descricao"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione uma descrição</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_categoria_tematica" class="block text-sm font-medium text-gray-700 mb-2">Categoria Temática</label>
                        <select id="assunto_categoria_tematica"
                                x-model="formData.assunto.categoria_tematica"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione uma categoria temática</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_sugestao1" class="block text-sm font-medium text-gray-700 mb-2">Sugestão 1</label>
                        <select id="assunto_sugestao1"
                                x-model="formData.assunto.sugestao1"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione sugestão 1</option>
                        </select>
                    </div>

                    <div>
                        <label for="assunto_extra" class="block text-sm font-medium text-gray-700 mb-2">Extra</label>
                        <select id="assunto_extra"
                                x-model="formData.assunto.extra"
                                @change="saveToSession()"
                                class="w-full select2-assunto">
                            <option value="">Selecione extra</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 5: Serviço (Main Input Section) -->
            <div x-show="currentStep === 5" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-accent text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">6</span>
                    Serviço
                    <span class="ml-2 text-sm bg-accent text-white px-2 py-1 rounded-full">Principal</span>
                </h2>

                <div class="bg-accent-light bg-opacity-10 border border-accent rounded-lg p-4 mb-6">
                    <p class="text-accent-dark text-sm">
                        <strong>Seção Principal:</strong> Esta é a única seção onde você pode inserir dados personalizados.
                        Preencha as informações do serviço conforme necessário.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="servico_codigo" class="block text-sm font-medium text-gray-700 mb-2">
                            Código do Serviço
                        </label>
                        <input type="text"
                               id="servico_codigo"
                               x-model="formData.servico.codigo"
                               @input="saveToSession()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
                               placeholder="Digite o código do serviço">
                    </div>

                    <div>
                        <label for="servico_tipo_servico" class="block text-sm font-medium text-gray-700 mb-2">
                            Tipo de Serviço
                        </label>
                        <input type="text"
                               id="servico_tipo_servico"
                               x-model="formData.servico.tipo_servico"
                               @input="saveToSession()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
                               placeholder="Digite o tipo de serviço">
                    </div>

                    <div class="md:col-span-2">
                        <label for="servico_comentarios" class="block text-sm font-medium text-gray-700 mb-2">
                            Comentários
                        </label>
                        <textarea id="servico_comentarios"
                                  x-model="formData.servico.comentarios"
                                  @input="saveToSession()"
                                  rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent"
                                  placeholder="Digite comentários adicionais sobre o serviço"></textarea>
                    </div>
                </div>
            </div>

            <!-- Step 6: Local -->
            <div x-show="currentStep === 6" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-secondary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">7</span>
                    Local
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="local_codigo" class="block text-sm font-medium text-gray-700 mb-2">Código</label>
                        <select id="local_codigo"
                                x-model="formData.local.codigo"
                                @change="saveToSession()"
                                class="w-full select2-local">
                            <option value="">Selecione um código</option>
                        </select>
                    </div>

                    <div>
                        <label for="local_localizacao" class="block text-sm font-medium text-gray-700 mb-2">Localização</label>
                        <select id="local_localizacao"
                                x-model="formData.local.localizacao"
                                @change="saveToSession()"
                                class="w-full select2-local">
                            <option value="">Selecione uma localização</option>
                        </select>
                    </div>

                    <div>
                        <label for="local_sugestao" class="block text-sm font-medium text-gray-700 mb-2">Sugestão</label>
                        <select id="local_sugestao"
                                x-model="formData.local.sugestao"
                                @change="saveToSession()"
                                class="w-full select2-local">
                            <option value="">Selecione uma sugestão</option>
                        </select>
                    </div>

                    <div>
                        <label for="local_comentarios" class="block text-sm font-medium text-gray-700 mb-2">Comentários</label>
                        <select id="local_comentarios"
                                x-model="formData.local.comentarios"
                                @change="saveToSession()"
                                class="w-full select2-local">
                            <option value="">Selecione comentários</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 7: Especialidade -->
            <div x-show="currentStep === 7" class="form-step">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                    <span class="bg-highlight text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">8</span>
                    Especialidade
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label for="especialidade_codigo" class="block text-sm font-medium text-gray-700 mb-2">Código</label>
                        <select id="especialidade_codigo"
                                x-model="formData.especialidade.codigo"
                                @change="saveToSession()"
                                class="w-full select2-especialidade">
                            <option value="">Selecione um código</option>
                        </select>
                    </div>

                    <div>
                        <label for="especialidade_especialidade" class="block text-sm font-medium text-gray-700 mb-2">Especialidade</label>
                        <select id="especialidade_especialidade"
                                x-model="formData.especialidade.especialidade"
                                @change="saveToSession()"
                                class="w-full select2-especialidade">
                            <option value="">Selecione uma especialidade</option>
                        </select>
                    </div>

                    <div>
                        <label for="especialidade_sugestao" class="block text-sm font-medium text-gray-700 mb-2">Sugestão</label>
                        <select id="especialidade_sugestao"
                                x-model="formData.especialidade.sugestao"
                                @change="saveToSession()"
                                class="w-full select2-especialidade">
                            <option value="">Selecione uma sugestão</option>
                        </select>
                    </div>

                    <div>
                        <label for="especialidade_comentarios" class="block text-sm font-medium text-gray-700 mb-2">Comentários</label>
                        <select id="especialidade_comentarios"
                                x-model="formData.especialidade.comentarios"
                                @change="saveToSession()"
                                class="w-full select2-especialidade">
                            <option value="">Selecione comentários</option>
                        </select>
                    </div>

                    <div class="md:col-span-2 lg:col-span-1">
                        <label for="especialidade_extra" class="block text-sm font-medium text-gray-700 mb-2">Extra</label>
                        <select id="especialidade_extra"
                                x-model="formData.especialidade.extra"
                                @change="saveToSession()"
                                class="w-full select2-especialidade">
                            <option value="">Selecione extra</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="flex justify-between items-center">
            <button type="button"
                    @click="previousStep()"
                    x-show="currentStep > 0"
                    class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                ← Anterior
            </button>

            <div x-show="currentStep === 0" class="w-24"></div>

            <button type="button"
                    @click="nextStep()"
                    x-show="currentStep < 7"
                    class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors">
                Próximo →
            </button>

            <button type="button"
                    @click="submitForm()"
                    x-show="currentStep === 7"
                    class="px-6 py-2 bg-accent text-white rounded-md hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 transition-colors">
                Finalizar Cadastro
            </button>
        </div>

        <!-- Success Modal -->
        <div x-show="showSuccessModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             style="display: none;">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Cadastro Realizado com Sucesso!</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                            Suas informações foram enviadas com sucesso. Você receberá uma confirmação por e-mail em breve.
                        </p>
                    </div>
                    <div class="items-center px-4 py-3">
                        <button @click="closeSuccessModal()"
                                class="px-4 py-2 bg-primary text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary">
                            Fechar
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div x-show="isLoading"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-40"
             style="display: none;">
            <div class="relative top-1/2 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white transform -translate-y-1/2">
                <div class="mt-3 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-light bg-opacity-20">
                        <svg class="animate-spin h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4">Processando...</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                            Aguarde enquanto processamos suas informações.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alpine.js Form Logic -->
    <script>
        function seiForm() {
            return {
                currentStep: 0,
                isLoading: false,
                showSuccessModal: false,
                errors: [],

                steps: [
                    { title: 'Requisitante', key: 'requisitante' },
                    { title: 'Unidade', key: 'unidade' },
                    { title: 'Interessado', key: 'interessado' },
                    { title: 'Localização', key: 'localizacao' },
                    { title: 'Assunto', key: 'assunto' },
                    { title: 'Serviço', key: 'servico' },
                    { title: 'Local', key: 'local' },
                    { title: 'Especialidade', key: 'especialidade' }
                ],

                formData: {
                    requisitante: {
                        nome: '',
                        email: '',
                        email_confirm: ''
                    },
                    unidade: {
                        codigo: '',
                        unidade: ''
                    },
                    interessado: {
                        codigo: '',
                        concessao: '',
                        sugestao: '',
                        comentarios: ''
                    },
                    localizacao: {
                        codigo: '',
                        localizacao: '',
                        obs: '',
                        sugestao: '',
                        comentarios: '',
                        codificacao: '',
                        legenda: ''
                    },
                    assunto: {
                        codigo: '',
                        doc: '',
                        sugestao: '',
                        obs1: '',
                        obs2: '',
                        obs3: '',
                        sigla_padronizada: '',
                        sigla_original: '',
                        descricao: '',
                        categoria_tematica: '',
                        sugestao1: '',
                        extra: ''
                    },
                    servico: {
                        codigo: '',
                        tipo_servico: '',
                        comentarios: ''
                    },
                    local: {
                        codigo: '',
                        localizacao: '',
                        sugestao: '',
                        comentarios: ''
                    },
                    especialidade: {
                        codigo: '',
                        especialidade: '',
                        sugestao: '',
                        comentarios: '',
                        extra: ''
                    }
                },

                init() {
                    // Load data from session storage
                    this.loadFromSession();

                    // Initialize Select2 dropdowns
                    this.$nextTick(() => {
                        this.initializeSelect2();
                    });
                },

                validateStep(stepIndex) {
                    this.errors = [];

                    if (stepIndex === 0) {
                        // Validate Requisitante step
                        if (!this.formData.requisitante.nome.trim()) {
                            this.errors.push('Nome é obrigatório');
                        }
                        if (!this.formData.requisitante.email.trim()) {
                            this.errors.push('E-mail é obrigatório');
                        }
                        if (!this.formData.requisitante.email_confirm.trim()) {
                            this.errors.push('Confirmação de e-mail é obrigatória');
                        }
                        if (this.formData.requisitante.email !== this.formData.requisitante.email_confirm) {
                            this.errors.push('E-mails não coincidem');
                        }

                        // Basic email validation
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (this.formData.requisitante.email && !emailRegex.test(this.formData.requisitante.email)) {
                            this.errors.push('E-mail inválido');
                        }
                    }

                    return this.errors.length === 0;
                },

                isStepValid(stepIndex) {
                    // For now, only validate the first step (Requisitante)
                    if (stepIndex === 0) {
                        return this.formData.requisitante.nome.trim() &&
                               this.formData.requisitante.email.trim() &&
                               this.formData.requisitante.email_confirm.trim() &&
                               this.formData.requisitante.email === this.formData.requisitante.email_confirm;
                    }
                    return true; // Other steps are optional for now
                },

                nextStep() {
                    if (this.validateStep(this.currentStep)) {
                        if (this.currentStep < 7) {
                            this.currentStep++;
                            this.saveToSession();
                            this.scrollToTop();
                        }
                    }
                },

                previousStep() {
                    if (this.currentStep > 0) {
                        this.currentStep--;
                        this.errors = []; // Clear errors when going back
                        this.saveToSession();
                        this.scrollToTop();
                    }
                },

                navigateToStep(stepIndex) {
                    // Only allow navigation to completed steps or the next step
                    if (stepIndex <= this.currentStep || (stepIndex === this.currentStep + 1 && this.isStepValid(this.currentStep))) {
                        if (stepIndex < this.currentStep || this.validateStep(this.currentStep)) {
                            this.currentStep = stepIndex;
                            this.saveToSession();
                            this.scrollToTop();
                        }
                    }
                },

                scrollToTop() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                },

                saveToSession() {
                    // Save current step and form data to session storage
                    sessionStorage.setItem('seiFormStep', this.currentStep);
                    sessionStorage.setItem('seiFormData', JSON.stringify(this.formData));
                },

                loadFromSession() {
                    // Load step and form data from session storage
                    const savedStep = sessionStorage.getItem('seiFormStep');
                    const savedData = sessionStorage.getItem('seiFormData');

                    if (savedStep !== null) {
                        this.currentStep = parseInt(savedStep);
                    }

                    if (savedData) {
                        try {
                            const parsedData = JSON.parse(savedData);
                            this.formData = { ...this.formData, ...parsedData };
                        } catch (e) {
                            console.error('Error parsing saved form data:', e);
                        }
                    }
                },

                clearSession() {
                    sessionStorage.removeItem('seiFormStep');
                    sessionStorage.removeItem('seiFormData');
                },

                async submitForm() {
                    if (!this.validateStep(this.currentStep)) {
                        return;
                    }

                    this.isLoading = true;

                    try {
                        // Simulate API call - replace with actual endpoint
                        const response = await fetch('/api/sei/submit/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': this.getCSRFToken()
                            },
                            body: JSON.stringify(this.formData)
                        });

                        if (response.ok) {
                            this.showSuccessModal = true;
                            this.clearSession();
                        } else {
                            throw new Error('Erro ao enviar formulário');
                        }
                    } catch (error) {
                        console.error('Error submitting form:', error);
                        this.errors = ['Erro ao enviar formulário. Tente novamente.'];
                    } finally {
                        this.isLoading = false;
                    }
                },

                closeSuccessModal() {
                    this.showSuccessModal = false;
                    // Reset form
                    this.currentStep = 0;
                    this.formData = {
                        requisitante: { nome: '', email: '', email_confirm: '' },
                        unidade: { codigo: '', unidade: '' },
                        interessado: { codigo: '', concessao: '', sugestao: '', comentarios: '' },
                        localizacao: { codigo: '', localizacao: '', obs: '', sugestao: '', comentarios: '', codificacao: '', legenda: '' },
                        assunto: { codigo: '', doc: '', sugestao: '', obs1: '', obs2: '', obs3: '', sigla_padronizada: '', sigla_original: '', descricao: '', categoria_tematica: '', sugestao1: '', extra: '' },
                        servico: { codigo: '', tipo_servico: '', comentarios: '' },
                        local: { codigo: '', localizacao: '', sugestao: '', comentarios: '' },
                        especialidade: { codigo: '', especialidade: '', sugestao: '', comentarios: '', extra: '' }
                    };
                },

                getCSRFToken() {
                    // Get CSRF token from Django
                    const cookies = document.cookie.split(';');
                    for (let cookie of cookies) {
                        const [name, value] = cookie.trim().split('=');
                        if (name === 'csrftoken') {
                            return value;
                        }
                    }
                    return '';
                },

                initializeSelect2() {
                    // Initialize Select2 for all select elements
                    const selectElements = [
                        '.select2-unidade',
                        '.select2-interessado',
                        '.select2-localizacao',
                        '.select2-assunto',
                        '.select2-local',
                        '.select2-especialidade'
                    ];

                    selectElements.forEach(selector => {
                        $(selector).select2({
                            placeholder: 'Selecione uma opção',
                            allowClear: true,
                            ajax: {
                                url: this.getAjaxUrl(selector),
                                dataType: 'json',
                                delay: 250,
                                data: function (params) {
                                    return {
                                        q: params.term,
                                        page: params.page || 1
                                    };
                                },
                                processResults: function (data, params) {
                                    params.page = params.page || 1;
                                    return {
                                        results: data.results,
                                        pagination: {
                                            more: data.pagination && data.pagination.more
                                        }
                                    };
                                },
                                cache: true
                            }
                        });
                    });
                },

                getAjaxUrl(selector) {
                    // Map selectors to API endpoints
                    const urlMap = {
                        '.select2-unidade': '/api/sei/unidade/',
                        '.select2-interessado': '/api/sei/interessado/',
                        '.select2-localizacao': '/api/sei/localizacao/',
                        '.select2-assunto': '/api/sei/assunto/',
                        '.select2-local': '/api/sei/local/',
                        '.select2-especialidade': '/api/sei/especialidade/'
                    };
                    return urlMap[selector] || '/api/sei/default/';
                }
            }
        }
    </script>
</body>
</html>