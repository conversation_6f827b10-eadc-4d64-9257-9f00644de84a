<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Protocolo {{ protocolo.doc_cod }} - Sistema SEI</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF161F',
                        'primary-dark': '#E6141B',
                        accent: '#FF6B35',
                        'accent-dark': '#E6602F'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">Sistema SEI</h1>
                    <span class="ml-4 text-gray-500">Editar Protocolo</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'sei:protocolo_detail' protocolo_id=protocolo.id %}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Voltar aos Detalhes
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Info Message -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-blue-800">
                        Funcionalidade de Edição
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>A funcionalidade de edição de protocolos está em desenvolvimento. Por enquanto, você pode visualizar os detalhes do protocolo e criar novos protocolos.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Protocol Information (Read-only for now) -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
            <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Protocolo: {{ protocolo.doc_cod }}</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- Requisitante Information -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">Dados do Requisitante</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Nome Completo</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.usuario.nome|default:"Não informado" }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">E-mail</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.usuario.email|default:"Não informado" }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Service Information -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">Informações do Serviço</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Código do Serviço</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.servico_codigo|default:"Não informado" }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Tipo de Serviço</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.servico_tipo|default:"Não informado" }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">Informações Adicionais</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Revisão</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.doc_revisao|default:"Não aplicável" }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Número SEI</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.doc_sei_num|default:"Não aplicável" }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data de Criação</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.created_at|date:"d/m/Y H:i" }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Última Atualização</label>
                                <div class="px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900">
                                    {{ protocolo.updated_at|date:"d/m/Y H:i" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Placeholder Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Edição de Protocolo</h3>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Em Desenvolvimento
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>A funcionalidade de edição completa está sendo desenvolvida. Atualmente você pode:</p>
                                <ul class="list-disc list-inside mt-2 space-y-1">
                                    <li>Visualizar todos os detalhes do protocolo</li>
                                    <li>Fazer download do PDF</li>
                                    <li>Criar novos protocolos</li>
                                    <li>Excluir protocolos existentes</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <div class="text-center">
                        <p class="text-gray-500 mb-4">
                            A edição de protocolos será implementada em uma versão futura do sistema.
                        </p>
                        
                        <button type="submit" 
                                disabled
                                class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gray-400 cursor-not-allowed">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Salvar Alterações (Em Desenvolvimento)
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
            <a href="{% url 'sei:protocolo_detail' protocolo_id=protocolo.id %}" 
               class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Ver Detalhes
            </a>
            
            <a href="{% url 'sei:protocolo_list' %}" 
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                </svg>
                Voltar à Lista
            </a>
            
            <a href="{% url 'sei:sei_form' %}" 
               class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Criar Novo Protocolo
            </a>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <p class="text-sm text-gray-300">
                    © 2025 Sistema SEI - Todos os direitos reservados
                </p>
            </div>
        </div>
    </footer>
</body>
</html>
