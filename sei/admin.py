from django.contrib import admin
from .models import (
    Requisitante, Unidade, Interessado, Localizacao, Assunto,
    Local, Disciplina, Protocolo
)

@admin.register(Requisitante)
class RequisitanteAdmin(admin.ModelAdmin):
    list_display = ('nome', 'email',)
    search_fields = ('nome', 'email')

@admin.register(Unidade)
class UnidadeAdmin(admin.ModelAdmin):
    list_display = ('unidade', 'codigo', 'created_at', 'updated_at')
    search_fields = ('unidade', 'codigo')

@admin.register(Interessado)
class InteressadoAdmin(admin.ModelAdmin):
    list_display = ('codigo', 'concessao', 'created_at', 'updated_at')
    search_fields = ('codigo', 'concessao')

@admin.register(Localizacao)
class LocalizacaoAdmin(admin.ModelAdmin):
    list_display = ('localizacao', 'codigo', 'created_at', 'updated_at')
    search_fields = ('localizacao', 'codigo')

@admin.register(Assunto)
class AssuntoAdmin(admin.ModelAdmin):
    list_display = ('descricao', 'codigo', 'created_at', 'updated_at')
    search_fields = ('descricao', 'codigo')

@admin.register(Protocolo)
class ProtocoloAdmin(admin.ModelAdmin):
    list_display = ('doc_cod', 'servico_codigo', 'servico_tipo', 'created_at', 'updated_at')
    search_fields = ('doc_cod', 'servico_codigo', 'servico_tipo')
    list_filter = ('created_at', 'updated_at')
    readonly_fields = ('doc_cod', 'created_at', 'updated_at')

@admin.register(Local)
class LocalAdmin(admin.ModelAdmin):
    list_display = ('local', 'codigo', 'created_at', 'updated_at')
    search_fields = ('local', 'codigo')

@admin.register(Disciplina)
class DisciplinaAdmin(admin.ModelAdmin):
    list_display = ('disciplina', 'codigo', 'created_at', 'updated_at')
    search_fields = ('disciplina', 'codigo')