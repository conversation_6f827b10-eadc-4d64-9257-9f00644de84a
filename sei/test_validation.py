"""
Test script to verify backend validation for SEI form submission.
This can be run manually to test the validation logic.
"""

from django.test import TestCase, Client
from django.urls import reverse
import json


class SeiFormValidationTest(TestCase):
    """Test cases for SEI form validation"""
    
    def setUp(self):
        self.client = Client()
        self.url = reverse('sei:form_submit')
        
        # Base valid data
        self.valid_data = {
            'requisitante': {
                'nome': '<PERSON>',
                'email': '<EMAIL>'
            },
            'unidade_id': None,
            'interessado_id': None,
            'localizacao_id': None,
            'assunto_id': None,
            'servico_codigo': '123',
            'servico_tipo': '<PERSON><PERSON><PERSON>e Técnica',
            'local_id': None,
            'disciplina_id': None,
            'doc_revisao': '',
            'doc_sei_num': ''
        }
    
    def test_nome_validation_success(self):
        """Test successful nome validation with spaces"""
        test_cases = [
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',  # minimum 3 chars
            'A' * 150  # maximum 150 chars
        ]
        
        for nome in test_cases:
            with self.subTest(nome=nome):
                data = self.valid_data.copy()
                data['requisitante']['nome'] = nome
                
                # This would normally fail due to missing required fields,
                # but we're testing the nome validation specifically
                response = self.client.post(
                    self.url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                # Should not fail due to nome validation
                self.assertNotIn('Nome deve ter', response.json().get('message', ''))
    
    def test_nome_validation_failure(self):
        """Test nome validation failures"""
        test_cases = [
            ('', 'Nome é obrigatório'),
            ('  ', 'Nome é obrigatório'),  # only spaces
            ('Jo', 'Nome deve ter pelo menos 3 caracteres'),
            ('A' * 151, 'Nome deve ter no máximo 150 caracteres')
        ]
        
        for nome, expected_error in test_cases:
            with self.subTest(nome=nome):
                data = self.valid_data.copy()
                data['requisitante']['nome'] = nome
                
                response = self.client.post(
                    self.url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, 400)
                self.assertIn(expected_error, response.json().get('message', ''))
    
    def test_servico_tipo_validation_success(self):
        """Test successful servico_tipo validation"""
        test_cases = [
            '',  # empty is valid (optional)
            'Análise Técnica',
            'Consultoria Especializada',
            'ABC',  # minimum 3 chars
            'A' * 255  # maximum 255 chars
        ]
        
        for servico_tipo in test_cases:
            with self.subTest(servico_tipo=servico_tipo):
                data = self.valid_data.copy()
                data['servico_tipo'] = servico_tipo
                
                response = self.client.post(
                    self.url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                # Should not fail due to servico_tipo validation
                self.assertNotIn('Tipo de serviço deve ter', response.json().get('message', ''))
    
    def test_servico_tipo_validation_failure(self):
        """Test servico_tipo validation failures"""
        test_cases = [
            ('AB', 'Tipo de serviço deve ter pelo menos 3 caracteres'),
            ('A' * 256, 'Tipo de serviço deve ter no máximo 255 caracteres')
        ]
        
        for servico_tipo, expected_error in test_cases:
            with self.subTest(servico_tipo=servico_tipo):
                data = self.valid_data.copy()
                data['servico_tipo'] = servico_tipo
                
                response = self.client.post(
                    self.url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, 400)
                self.assertIn(expected_error, response.json().get('message', ''))
    
    def test_email_validation_failure(self):
        """Test email validation with spaces"""
        test_cases = [
            ('user @example.com', 'E-mail não pode conter espaços'),
            ('user@ example.com', 'E-mail não pode conter espaços'),
            ('user@example .com', 'E-mail não pode conter espaços')
        ]
        
        for email, expected_error in test_cases:
            with self.subTest(email=email):
                data = self.valid_data.copy()
                data['requisitante']['email'] = email
                
                response = self.client.post(
                    self.url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, 400)
                self.assertIn(expected_error, response.json().get('message', ''))
    
    def test_servico_codigo_validation_failure(self):
        """Test servico_codigo numeric validation"""
        test_cases = [
            ('123abc', 'Código do serviço deve conter apenas números'),
            ('12 34', 'Código do serviço deve conter apenas números'),
            ('abc', 'Código do serviço deve conter apenas números')
        ]
        
        for codigo, expected_error in test_cases:
            with self.subTest(codigo=codigo):
                data = self.valid_data.copy()
                data['servico_codigo'] = codigo
                
                response = self.client.post(
                    self.url,
                    data=json.dumps(data),
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, 400)
                self.assertIn(expected_error, response.json().get('message', ''))


if __name__ == '__main__':
    # Simple manual test runner
    print("Testing SEI form validation...")
    
    # You can add manual test calls here if needed
    print("Tests completed. Run with Django test runner for full validation.")
