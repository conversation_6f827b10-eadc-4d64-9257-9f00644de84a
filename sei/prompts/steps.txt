step 0:
user enters name, email and confirmation of email
validation is done over all fields
can't past anything in the email and confirmation email fields

step 1:
just one select box. The user click in the select2 field that list some options (3 for now). If none is selected, the user can't go to the next step.the user must start typing to make an api call and return the values found in the database for that "Unidade"
the values are the string representation of the model.
After selecting the option, the user can go to the next step.

step 2:
same as step 1, but for the "Interessado" model. 
The user can select one of the options returned by the api call. The options must be just the value of "concessao"
the search can be in fields "codigo" or "concessao"

step 3:
same as step 2, but for the "Localizacao" model. 
The user can select one of the options returned by the api call. The options must be just the value of "localizacao"
the search can be in fields "codigo" or "localizacao"

step 4:
same as step 3, but for the "Assunto" model. 
The user can select one of the options returned by the api call. The options must be just the value of "doc"
the search can be in fields "codigo" or "doc"

step 5:
the only step that has two text inputs for the user.
these fields aren't mandatory, the user can just go to the next step without filling them.

step 6: 
same as step 4, but for "Local" model.
The user can select one of the options returned by the api call. The options must be just the value of "local"
the search can be in fields "codigo" or "local"

step 7:
same as step 4, but for "Especialidade" model.
The user can select one of the options returned by the api call. The options must be just the value of "especialidade"
the search can be in fields "codigo" or "especialidade"

step 8:
show all the values inputed or selected by the user in the previous steps.
the user can't change anything here.
the user can submit the form or go back to the previous step.


for all steps, always returns the string representation of the object to the user
